<% layout('layout') -%>

<%
  let slotsBarWidth = 0;
  if (quota.streaming.max > 0) {
    slotsBarWidth = (quota.streaming.current / quota.streaming.max) * 100;
  }
%>

  <!-- Load Balancer Notification -->
  <div id="loadBalancerNotification" class="hidden fixed top-20 right-4 z-50 bg-yellow-600 text-white px-4 py-3 rounded-lg shadow-lg max-w-sm">
    <div class="flex items-center space-x-2">
      <i class="ti ti-adjustments text-lg"></i>
      <div>
        <div class="font-medium">Quality Adjusted</div>
        <div id="notificationMessage" class="text-sm opacity-90"></div>
      </div>
      <button onclick="hideNotification()" class="ml-2 text-white hover:text-gray-200">
        <i class="ti ti-x"></i>
      </button>
    </div>
  </div>

  <!-- Stats Cards - Responsive for both mobile and desktop -->
  <div class="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6 mb-6">
    <div class="card-enhanced p-6">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-semibold"><%= t('dashboard.active_streams') %></h3>
        <div class="w-10 h-10 bg-gradient-to-br from-primary to-secondary rounded-lg flex items-center justify-center">
          <i class="ti ti-device-tv text-xl text-white"></i>
        </div>
      </div>
      <p class="text-3xl font-bold mt-2 gradient-text" id="active-streams-count">0</p>
      <p class="text-sm text-gray-400 mt-1" id="active-streams-status"><%= t('dashboard.no_streams') %></p>
    </div>
    <div class="card-enhanced p-6">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-semibold"><%= t('dashboard.streaming_quota') %></h3>
        <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center">
          <i class="ti ti-broadcast text-xl text-white"></i>
        </div>
      </div>
      <p class="text-3xl font-bold mt-2">
        <span id="slots-used" class="gradient-text"><%= quota.streaming.current %></span><span class="text-sm text-gray-400"> / <%= quota.streaming.max === -1 ? '∞' : quota.streaming.max %></span>
      </p>
      <div class="w-full bg-gray-700 rounded-full h-2.5 mt-2">
        <div id="slots-bar" class="bg-gradient-to-r from-green-500 to-green-600 h-2.5 rounded-full transition-all duration-300" data-width="<%= slotsBarWidth %>"></div>
      </div>
      <p class="text-sm text-gray-400 mt-2">
        <i class="ti ti-info-circle mr-1"></i>
        <%= t('dashboard.streaming_quota_desc') %>
      </p>
    </div>

  </div>
  <div class="mt-8">
    <div class="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between mb-6">
      <h2 class="text-xl font-bold"><%= t('dashboard.my_streams') %></h2>
      <div class="flex flex-col sm:flex-row items-center gap-3 w-full sm:w-auto">
        <div class="relative w-full sm:w-64">
          <input type="text" placeholder="<%= t('common.search') %> streams..."
            class="w-full bg-dark-700 border border-gray-600 text-white pl-9 pr-4 py-2 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary">
          <i class="ti ti-search absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"></i>
        </div>
        <button id="newStreamBtn" onclick="checkSlotAvailabilityAndOpenModal()"
          class="btn-primary-enhanced w-full sm:w-auto flex items-center justify-center gap-2 disabled:bg-gray-600 disabled:cursor-not-allowed disabled:hover:bg-gray-600">
          <i class="ti ti-plus"></i>
          <span><%= t('dashboard.create_stream') %></span>
        </button>
      </div>
    </div>
    <!-- Streams Grid - Responsive for both mobile and desktop -->
    <div id="streams-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 gap-4 md:gap-6">
      <!-- Streams will be populated here -->
    </div>

    <!-- Empty State -->
    <div id="streams-empty-state" class="hidden">
      <div class="card-enhanced p-8 md:p-12 text-center">
        <div class="flex flex-col items-center">
          <div class="w-16 h-16 md:w-20 md:h-20 rounded-full bg-dark-700 flex items-center justify-center mb-4 md:mb-6">
            <i class="ti ti-video-plus text-gray-500 text-2xl md:text-3xl"></i>
          </div>
          <h3 class="text-lg md:text-xl font-semibold text-gray-300 mb-2"><%= t('dashboard.no_streams') %></h3>
          <p class="text-gray-500 max-w-md mb-4 md:mb-6 text-sm md:text-base"><%= t('dashboard.create_stream_description') %></p>
          <button id="main-action-button" onclick="checkSlotAvailabilityAndOpenModal()"
            class="btn-primary-enhanced flex items-center gap-2">
            <i class="ti ti-plus" id="main-action-icon"></i>
            <span id="main-action-text"><%= t('dashboard.create_stream') %></span>
          </button>
        </div>
      </div>
    </div>
  </div>
  <div id="newStreamModal" class="fixed inset-0 bg-black/50 z-50 hidden modal-overlay overflow-y-auto">
    <div class="flex min-h-screen items-center justify-center p-4">
      <div class="bg-dark-800 rounded-lg shadow-xl w-full max-w-4xl modal-container flex flex-col max-h-[90vh]">
        <div class="flex-shrink-0 flex items-center justify-between p-4 sm:px-6 sm:py-6 border-b border-gray-700">
          <h3 class="text-lg font-semibold"><%= t('streams.create_new') %></h3>
          <button onclick="closeNewStreamModal()" class="text-gray-400 hover:text-white">
            <i class="ti ti-x text-xl"></i>
          </button>
        </div>
        <div class="p-4 sm:px-6 pt-1 pb-4 overflow-y-auto flex-grow">
          <form id="newStreamForm" class="space-y-6" onsubmit="console.log('🛡️ Form onsubmit triggered - preventing default'); event.preventDefault(); event.stopPropagation(); return false;">
            <!-- Immediate form prevention script -->
            <script>
              console.log('🛡️ Immediate form prevention script loaded');
              (function() {
                const form = document.getElementById('newStreamForm');
                if (form) {
                  console.log('🛡️ Applying immediate form prevention');
                  form.addEventListener('submit', function(e) {
                    console.log('🛡️ Form submit event captured by immediate script');
                    e.preventDefault();
                    e.stopPropagation();
                    return false;
                  }, true);

                  // Also prevent the submit button
                  const submitBtn = document.querySelector('button[type="submit"][form="newStreamForm"]');
                  if (submitBtn) {
                    console.log('🛡️ Applying immediate button prevention');
                    submitBtn.addEventListener('click', function(e) {
                      console.log('🛡️ Submit button click captured by immediate script');
                      e.preventDefault();
                      e.stopPropagation();
                      return false;
                    }, true);
                  }
                }
              })();
            </script>
            <input type="hidden" name="_csrf" value="<%= csrfToken %>">
            <input type="hidden" id="selectedVideoId" name="videoId" value="">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div class="space-y-4">
                <div class="relative">
                  <label class="text-sm font-medium text-white block mb-2"><%= t('streams.video_file') %></label>
                  <div class="relative">
                    <button type="button" onclick="toggleVideoSelector()"
                      class="w-full flex items-center justify-between px-4 py-2.5 bg-dark-700 border border-gray-600 rounded-lg hover:border-primary focus:border-primary focus:ring-1 focus:ring-primary transition-colors text-left">
                      <span class="text-sm text-gray-300" id="selectedVideo"><%= t('streams.video_file') %>...</span>
                      <i class="ti ti-chevron-down text-gray-400"></i>
                    </button>
                    <div id="videoSelectorDropdown"
                      class="hidden absolute z-[70] mt-2 w-full bg-dark-700 rounded-lg border border-gray-600 shadow-lg">
                      <div class="p-2 border-b border-gray-600/50">
                        <div class="relative">
                          <input type="text" id="videoSearchInput"
                            class="w-full bg-dark-800 text-white pl-8 pr-4 py-2 rounded-lg text-sm focus:outline-none focus:ring-1 focus:ring-primary border border-gray-700"
                            placeholder="<%= t('common.search') %> videos...">
                          <i class="ti ti-search absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"></i>
                        </div>
                      </div>
                      <div id="videoListContainer" class="p-2 space-y-1 max-h-60 overflow-y-auto">
                      </div>
                    </div>
                  </div>
                </div>
                <div class="lg:hidden bg-dark-900 rounded-lg overflow-hidden">
                  <div class="aspect-video bg-dark-900">
                    <div id="videoPreviewMobile" class="hidden w-full h-full">
                      <video id="videojs-preview-mobile" class="video-js vjs-default-skin vjs-big-play-centered"
                        controls preload="none" width="100%" height="100%"
                        data-setup='{"fluid": true, "playbackRates": [0.5, 1, 1.25, 1.5, 2]}'>
                        <p class="vjs-no-js">Please enable JavaScript to view videos</p>
                      </video>
                    </div>
                    <div id="emptyPreviewMobile" class="h-full flex flex-col items-center justify-center">
                      <i class="ti ti-video text-4xl text-gray-600 mb-2"></i>
                      <p class="text-sm text-gray-500"><%= t('streams.video_file') %></p>
                    </div>
                  </div>
                </div>
                <div>
                  <label class="text-sm font-medium text-white block mb-2"><%= t('streams.title') %></label>
                  <input type="text"
                    class="w-full px-4 py-2.5 bg-dark-700 border border-gray-600 rounded-lg focus:border-primary focus:ring-1 focus:ring-primary"
                    placeholder="<%= t('streams.title') %>..." name="streamTitle" id="streamTitle" required>
                </div>
                <div class="space-y-4">
                  <label class="text-sm font-medium text-white block">Stream Configuration</label>
                  <div class="space-y-3">
                    <div class="relative">
                      <input type="text" id="rtmpUrl" name="rtmpUrl"
                        class="w-full pl-10 pr-12 py-2.5 bg-dark-700 border border-gray-600 rounded-lg focus:border-primary focus:ring-1 focus:ring-primary"
                        placeholder="RTMP URL" required>
                      <i class="ti ti-link absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"></i>
                      <button type="button" id="platformSelector"
                        class="absolute right-2 top-1/2 -translate-y-1/2 w-8 h-8 flex items-center justify-center rounded-full hover:bg-dark-600 transition-colors"
                        aria-label="Select platform">
                        <i class="ti ti-list-check text-gray-400 hover:text-primary"></i>
                      </button>
                      <div id="platformDropdown"
                        class="hidden absolute z-[60] right-0 mt-1 w-48 bg-dark-700 rounded-lg border border-gray-600 shadow-lg overflow-hidden">
                        <div class="py-1">
                          <button type="button"
                            class="platform-option w-full flex items-center px-4 py-2 hover:bg-dark-600"
                            data-url="rtmp://a.rtmp.youtube.com/live2">
                            <i class="ti ti-brand-youtube text-red-500 text-base mr-2"></i>
                            <span class="text-sm">YouTube</span>
                          </button>
                          <button type="button"
                            class="platform-option w-full flex items-center px-4 py-2 hover:bg-dark-600"
                            data-url="rtmps://live-api-s.facebook.com:443/rtmp">
                            <i class="ti ti-brand-facebook text-blue-500 text-base mr-2"></i>
                            <span class="text-sm">Facebook</span>
                          </button>
                          <button type="button"
                            class="platform-option w-full flex items-center px-4 py-2 hover:bg-dark-600"
                            data-url="rtmps://ingest.global.live.prod.tiktok.com/live">
                            <i class="ti ti-brand-tiktok text-black text-base mr-2"></i>
                            <span class="text-sm">TikTok</span>
                          </button>
                          <button type="button"
                            class="platform-option w-full flex items-center px-4 py-2 hover:bg-dark-600"
                            data-url="rtmp://live.shopee.co.id/live">
                            <i class="ti ti-brand-shopee text-orange-500 text-base mr-2"></i>
                            <span class="text-sm">Shopee Live</span>
                          </button>
                          <button type="button"
                            class="platform-option w-full flex items-center px-4 py-2 hover:bg-dark-600"
                            data-url="rtmp://live.twitch.tv/live">
                            <i class="ti ti-brand-twitch text-purple-500 text-base mr-2"></i>
                            <span class="text-sm">Twitch</span>
                          </button>
                        </div>
                      </div>
                    </div>
                    <div class="relative">
                      <input type="password" id="streamKey" name="streamKey"
                        class="w-full pl-10 pr-12 py-2.5 bg-dark-700 border border-gray-600 rounded-lg focus:border-primary focus:ring-1 focus:ring-primary"
                        placeholder="Stream Key" required>
                      <i class="ti ti-key absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"></i>
                      <button type="button" onclick="toggleStreamKeyVisibility()"
                        class="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-white transition-colors">
                        <i class="ti ti-eye" id="streamKeyToggle"></i>
                      </button>
                    </div>
                  </div>

                  <!-- Validation Messages -->
                  <div id="rtmpValidationContainer" class="hidden mt-3">
                    <div id="rtmpValidationErrors" class="hidden space-y-1"></div>
                    <div id="rtmpValidationWarnings" class="hidden space-y-1"></div>
                  </div>

                  <!-- Platform Help -->
                  <div id="platformHelp" class="hidden mt-3 p-3 bg-primary/20 border border-primary/30 rounded-lg">
                    <div class="flex items-start">
                      <div class="flex-shrink-0">
                        <i class="ti ti-info-circle text-primary"></i>
                      </div>
                      <div class="ml-3">
                        <h4 class="text-sm font-medium text-primary" id="platformName"></h4>
                        <p class="text-sm text-gray-300 mt-1" id="platformInstructions"></p>
                        <p class="text-xs text-primary mt-1" id="platformKeyFormat"></p>
                        <div id="platformSuggestions" class="mt-2 space-y-1"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="hidden lg:block">
                <div class="overflow-hidden h-full">
                  <div class="aspect-video rounded-lg bg-dark-900">
                    <div id="videoPreview" class="hidden w-full h-full">
                      <video id="videojs-preview-desktop"
                        class="video-js vjs-default-skin vjs-big-play-centered rounded-lg" controls preload="none"
                        width="100%" height="100%"
                        data-setup='{"fluid": true, "playbackRates": [0.5, 1, 1.25, 1.5, 2]}'>
                        <p class="vjs-no-js">Please enable JavaScript to view videos</p>
                      </video>
                    </div>
                    <div id="emptyPreview" class="h-full flex flex-col items-center justify-center">
                      <i class="ti ti-video text-4xl text-gray-600 mb-2"></i>
                      <p class="text-sm text-gray-500">Select a video to preview</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="space-y-2">
              <div class="flex flex-col sm:flex-row sm:items-center">
                <label class="text-sm font-medium text-white">Schedule Settings</label>
                <span id="serverTimeDisplay"
                  class="mt-1 sm:mt-0 sm:ml-2 block sm:inline-block bg-gray-700 text-xs text-gray-300 px-2 py-0.5 rounded">
                  Server time: loading...
                </span>
              </div>
              <div class="grid grid-cols-1 sm:grid-cols-4 gap-4 sm:items-end">
                <div class="h-[42px] flex items-center justify-between">
                  <label class="text-sm text-gray-300">Loop Video</label>
                  <label class="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" name="loopVideo" id="loopVideo" class="sr-only peer" checked>
                    <div class="w-11 h-6 bg-dark-700 rounded-full peer peer-checked:bg-primary"></div>
                    <div
                      class="absolute left-[2px] top-[2px] w-5 h-5 bg-white rounded-full transition-all peer-checked:translate-x-5">
                    </div>
                  </label>
                </div>
                <div>
                  <label for="scheduleTime" class="text-xs text-gray-400 block mb-1"><%= t('streams.timezone.schedule_time_label') %></label>
                  <input type="datetime-local" id="scheduleTime" name="scheduleTime"
                    class="w-full h-[42px] px-4 bg-dark-700 border border-gray-600 rounded-lg focus:border-primary focus:ring-1 focus:ring-primary text-sm [color-scheme:dark]">
                  <div id="scheduleTimeError" class="text-red-400 text-xs mt-1 hidden">Schedule time cannot be in the past</div>
                </div>
                <div>
                  <label for="displayTimezone" class="text-xs text-gray-400 block mb-1">
                    <%= t('streams.timezone.display_in_timezone') %>
                    <span class="text-gray-500 font-normal">(<%= locale === 'en' ? 'for reference only' : 'hanya untuk referensi' %>)</span>
                  </label>
                  <select id="displayTimezone" name="displayTimezone" class="w-full h-[42px] px-4 bg-dark-700 border border-gray-600 rounded-lg focus:border-primary focus:ring-1 focus:ring-primary text-sm">
                    <!-- Options will be populated by JavaScript -->
                  </select>
                </div>
                <div>
                  <label for="duration" class="text-xs text-gray-400 block mb-1">Duration (minutes)</label>
                  <input type="number" min="0" id="duration" name="duration"
                    class="w-full h-[42px] px-4 bg-dark-700 border border-gray-600 rounded-lg focus:border-primary focus:ring-1 focus:ring-primary text-sm"
                    placeholder="0 = unlimited">
                </div>
              </div>
              <div class="mt-2 text-xs bg-dark-800/80 border border-gray-600/30 p-3 rounded-lg" id="scheduleHelperText">
                <div class="text-gray-300"><%= t('streams.timezone.helper_text_empty') %></div>
              </div>
            </div>
            <div class="space-y-4">
              <div class="pt-2 border-t border-gray-700">
                <button type="button" id="advancedSettingsToggle"
                  class="flex items-center justify-between w-full text-left">
                  <div class="flex items-center">
                    <span class="text-sm font-medium text-white">Advanced Settings</span>
                    <div class="relative ml-2 group">
                      <i class="ti ti-info-circle text-gray-400 hover:text-primary"></i>
                      <div
                        class="absolute bottom-full left-1/2 -translate-x-1/2 mb-2 hidden group-hover:block w-64 p-2 bg-dark-600 text-xs text-gray-200 rounded-md shadow-lg z-10">
                        <div class="text-center">Advanced settings akan disesuaikan otomatis agar tetap kompatibel dengan copy mode untuk performa optimal</div>
                        <div
                          class="absolute top-full left-1/2 -translate-x-1/2 border-4 border-transparent border-t-dark-600">
                        </div>
                      </div>
                    </div>
                    <span id="advancedSettingsPremiumBadge" class="hidden ml-2 px-2 py-0.5 text-xs bg-yellow-600 text-yellow-100 rounded-full">Premium</span>
                    <!-- Copy Mode Status Indicator -->
                    <div id="copyModeIndicator" class="hidden ml-2 px-2 py-0.5 text-xs bg-green-600 text-green-100 rounded-full">
                      <i class="ti ti-copy text-xs mr-1"></i>Copy Mode
                    </div>
                    <div id="reencodeIndicator" class="hidden ml-2 px-2 py-0.5 text-xs bg-orange-600 text-orange-100 rounded-full">
                      <i class="ti ti-settings text-xs mr-1"></i>Re-encode
                    </div>
                  </div>
                  <i class="ti ti-chevron-down text-gray-400 transition-transform"></i>
                </button>
                <div id="advancedSettingsContent" class="hidden space-y-6 pt-4">
                  <!-- Premium Plan Required Notice -->
                  <div id="advancedSettingsUpgradeNotice" class="hidden p-4 bg-yellow-600/20 border border-yellow-600/30 rounded-lg">
                    <div class="flex items-start">
                      <div class="flex-shrink-0">
                        <i class="ti ti-crown text-yellow-500 text-lg"></i>
                      </div>
                      <div class="ml-3">
                        <h4 class="text-sm font-medium text-yellow-400">Advanced Settings - Premium Feature</h4>
                        <p id="advancedSettingsUpgradeMessage" class="text-sm text-gray-300 mt-1">Advanced settings hanya tersedia di plan premium. Upgrade untuk mengakses fitur re-encoding video berkualitas tinggi.</p>
                        <div class="mt-3">
                          <a href="/subscription/plans" class="inline-flex items-center px-3 py-1.5 text-xs font-medium bg-yellow-600 hover:bg-yellow-700 text-white rounded-md transition-colors">
                            <i class="ti ti-arrow-up-right text-xs mr-1"></i>
                            Upgrade Plan
                          </a>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="space-y-4">
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <div>
                        <label class="text-xs text-gray-400 block mb-1">Bitrate</label>
                        <select name="bitrate" id="bitrateSelect"
                          class="w-full h-[42px] px-4 bg-dark-700 border border-gray-600 rounded-lg focus:border-primary focus:ring-1 focus:ring-primary text-sm">
                          <option value="2500" selected>2500 kbps</option>
                          <option value="4000">4000 kbps</option>
                          <option value="6000">6000 kbps</option>
                          <option value="8000">8000 kbps</option>
                          <option value="10000">10000 kbps</option>
                          <option value="12000">12000 kbps</option>
                          <option value="15000">15000 kbps</option>
                          <option value="20000">20000 kbps</option>
                        </select>
                      </div>
                      <div>
                        <label class="text-xs text-gray-400 block mb-1">Frame Rate</label>
                        <select name="fps" id="fpsSelect"
                          class="w-full h-[42px] px-4 bg-dark-700 border border-gray-600 rounded-lg focus:border-primary focus:ring-1 focus:ring-primary text-sm">
                          <option value="30" selected>30 FPS</option>
                          <option value="60">60 FPS</option>
                          <option value="120">120 FPS</option>
                        </select>
                      </div>
                      <div>
                        <label class="text-xs text-gray-400 block mb-1">Resolution</label>
                        <select id="resolutionSelect"
                          class="w-full h-[42px] px-4 bg-dark-700 border border-gray-600 rounded-lg focus:border-primary focus:ring-1 focus:ring-primary text-sm">
                          <option value="720" selected data-horizontal="1280x720" data-vertical="720x1280">720p HD
                          </option>
                          <option value="1080" data-horizontal="1920x1080" data-vertical="1080x1920">1080p Full HD
                          </option>
                          <option value="1440" data-horizontal="2560x1440" data-vertical="1440x2560">1440p QHD</option>
                          <option value="2160" data-horizontal="3840x2160" data-vertical="2160x3840">2160p 4K</option>
                        </select>
                        <div class="text-xs text-gray-500 mt-1">
                          <span id="currentResolution">1280x720</span>
                        </div>
                      </div>
                      <div>
                        <label class="text-xs text-gray-400 block mb-1">Orientation</label>
                        <div class="flex gap-2 h-[42px]">
                          <button type="button" onclick="setVideoOrientation('horizontal')"
                            class="flex-1 flex items-center justify-center bg-dark-700 hover:bg-dark-600 border border-gray-600 rounded-lg transition-colors active-orientation">
                            <i class="ti ti-rectangle text-sm mr-1"></i>
                            <span class="text-xs">Landscape</span>
                          </button>
                          <button type="button" onclick="setVideoOrientation('vertical')"
                            class="flex-1 flex items-center justify-center bg-dark-700 hover:bg-dark-600 border border-gray-600 rounded-lg transition-colors">
                            <i class="ti ti-rectangle-vertical text-sm mr-1"></i>
                            <span class="text-xs">Portrait</span>
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </form>
        </div>
        <div class="flex-shrink-0 flex items-center justify-end gap-3 p-4 sm:px-6 sm:py-6 border-t border-gray-700">
          <button onclick="closeNewStreamModal()"
            class="px-5 py-2.5 text-sm font-medium text-gray-300 hover:text-white transition-colors">
            Cancel
          </button>
          <button type="submit" form="newStreamForm"
            class="px-5 py-2.5 text-sm font-medium bg-primary hover:bg-secondary text-white rounded-lg transition-colors"
            onclick="console.log('🛡️ Submit button clicked - preventing default'); event.preventDefault(); event.stopPropagation(); return false;">
            Create Stream
          </button>
        </div>
      </div>
    </div>
  </div>
  <div id="editStreamModal" class="fixed inset-0 bg-black/50 z-50 hidden modal-overlay overflow-y-auto">
    <div class="flex min-h-screen items-center justify-center p-4">
      <div class="bg-dark-800 rounded-lg shadow-xl w-full max-w-4xl modal-container flex flex-col max-h-[90vh]">
        <div class="flex-shrink-0 flex items-center justify-between p-4 sm:px-6 sm:py-6 border-b border-gray-700">
          <h3 class="text-lg font-semibold">Edit Stream</h3>
          <button onclick="closeEditStreamModal()" class="text-gray-400 hover:text-white">
            <i class="ti ti-x text-xl"></i>
          </button>
        </div>
        <div class="p-4 sm:px-6 pt-1 pb-4 overflow-y-auto flex-grow">
          <form id="editStreamForm" class="space-y-6">
            <input type="hidden" name="_csrf" value="<%= csrfToken %>">
            <input type="hidden" id="editStreamId" name="streamId" value="">
            <input type="hidden" id="editSelectedVideoId" name="videoId" value="">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div class="space-y-4">
                <div class="relative">
                  <label class="text-sm font-medium text-white block mb-2">Select Video</label>
                  <div class="relative">
                    <button type="button" onclick="toggleEditVideoSelector()"
                      class="w-full flex items-center justify-between px-4 py-2.5 bg-dark-700 border border-gray-600 rounded-lg hover:border-primary focus:border-primary focus:ring-1 focus:ring-primary transition-colors text-left">
                      <span class="text-sm text-gray-300" id="editSelectedVideo">Choose a video...</span>
                      <i class="ti ti-chevron-down text-gray-400"></i>
                    </button>
                    <div id="editVideoSelectorDropdown"
                      class="hidden absolute z-[70] mt-2 w-full bg-dark-700 rounded-lg border border-gray-600 shadow-lg">
                      <div class="p-2 border-b border-gray-600/50">
                        <div class="relative">
                          <input type="text" id="editVideoSearchInput"
                            class="w-full bg-dark-800 text-white pl-8 pr-4 py-2 rounded-lg text-sm focus:outline-none focus:ring-1 focus:ring-primary border border-gray-700"
                            placeholder="Search videos...">
                          <i class="ti ti-search absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"></i>
                        </div>
                      </div>
                      <div id="editVideoListContainer" class="p-2 space-y-1 max-h-60 overflow-y-auto">
                      </div>
                    </div>
                  </div>
                </div>
                <div class="lg:hidden bg-dark-900 rounded-lg overflow-hidden">
                  <div class="aspect-video bg-dark-900">
                    <div id="editVideoPreviewMobile" class="hidden w-full h-full">
                      <video id="edit-videojs-preview-mobile" class="video-js vjs-default-skin vjs-big-play-centered"
                        controls preload="none" width="100%" height="100%"
                        data-setup='{"fluid": true, "playbackRates": [0.5, 1, 1.25, 1.5, 2]}'>
                        <p class="vjs-no-js">Please enable JavaScript to view videos</p>
                      </video>
                    </div>
                    <div id="editEmptyPreviewMobile" class="h-full flex flex-col items-center justify-center">
                      <i class="ti ti-video text-4xl text-gray-600 mb-2"></i>
                      <p class="text-sm text-gray-500">Select a video to preview</p>
                    </div>
                  </div>
                </div>
                <div>
                  <label class="text-sm font-medium text-white block mb-2">Stream Title</label>
                  <input type="text"
                    class="w-full px-4 py-2.5 bg-dark-700 border border-gray-600 rounded-lg focus:border-primary focus:ring-1 focus:ring-primary"
                    placeholder="Enter stream title..." name="streamTitle" id="editStreamTitle" required>
                </div>
                <div class="space-y-4">
                  <label class="text-sm font-medium text-white block mb-3">Stream Configuration</label>
                  <div class="space-y-3">
                    <div class="relative">
                      <input type="text" id="editRtmpUrl" name="rtmpUrl"
                        class="w-full pl-10 pr-12 py-2.5 bg-dark-700 border border-gray-600 rounded-lg focus:border-primary focus:ring-1 focus:ring-primary"
                        placeholder="RTMP URL" required>
                      <i class="ti ti-link absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"></i>
                      <button type="button" id="editPlatformSelector"
                        class="absolute right-2 top-1/2 -translate-y-1/2 w-8 h-8 flex items-center justify-center rounded-full hover:bg-dark-600 transition-colors"
                        aria-label="Select platform">
                        <i class="ti ti-list-check text-gray-400 hover:text-primary"></i>
                      </button>
                      <div id="editPlatformDropdown"
                        class="hidden absolute z-[60] right-0 mt-1 w-48 bg-dark-700 rounded-lg border border-gray-600 shadow-lg overflow-hidden">
                        <div class="py-1">
                          <button type="button"
                            class="platform-option w-full flex items-center px-4 py-2 hover:bg-dark-600"
                            data-url="rtmp://a.rtmp.youtube.com/live2">
                            <i class="ti ti-brand-youtube text-red-500 text-base mr-2"></i>
                            <span class="text-sm">YouTube</span>
                          </button>
                          <button type="button"
                            class="platform-option w-full flex items-center px-4 py-2 hover:bg-dark-600"
                            data-url="rtmps://live-api-s.facebook.com:443/rtmp">
                            <i class="ti ti-brand-facebook text-blue-500 text-base mr-2"></i>
                            <span class="text-sm">Facebook</span>
                          </button>
                          <button type="button"
                            class="platform-option w-full flex items-center px-4 py-2 hover:bg-dark-600"
                            data-url="rtmps://ingest.global.live.prod.tiktok.com/live">
                            <i class="ti ti-brand-tiktok text-black text-base mr-2"></i>
                            <span class="text-sm">TikTok</span>
                          </button>
                          <button type="button"
                            class="platform-option w-full flex items-center px-4 py-2 hover:bg-dark-600"
                            data-url="rtmp://live.shopee.co.id/live">
                            <i class="ti ti-brand-shopee text-orange-500 text-base mr-2"></i>
                            <span class="text-sm">Shopee Live</span>
                          </button>
                          <button type="button"
                            class="platform-option w-full flex items-center px-4 py-2 hover:bg-dark-600"
                            data-url="rtmp://live.twitch.tv/live">
                            <i class="ti ti-brand-twitch text-purple-500 text-base mr-2"></i>
                            <span class="text-sm">Twitch</span>
                          </button>
                        </div>
                      </div>
                    </div>
                    <div class="relative">
                      <input type="password" id="editStreamKey" name="streamKey"
                        class="w-full pl-10 pr-12 py-2.5 bg-dark-700 border border-gray-600 rounded-lg focus:border-primary focus:ring-1 focus:ring-primary"
                        placeholder="Stream Key" required>
                      <i class="ti ti-key absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"></i>
                      <button type="button" onclick="toggleEditStreamKeyVisibility()"
                        class="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-white transition-colors">
                        <i class="ti ti-eye" id="editStreamKeyToggle"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              <div class="hidden lg:block">
                <div class="overflow-hidden h-full">
                  <div class="aspect-video rounded-lg bg-dark-900">
                    <div id="editVideoPreview" class="hidden w-full h-full">
                      <video id="edit-videojs-preview-desktop"
                        class="video-js vjs-default-skin vjs-big-play-centered rounded-lg" controls preload="none"
                        width="100%" height="100%"
                        data-setup='{"fluid": true, "playbackRates": [0.5, 1, 1.25, 1.5, 2]}'>
                        <p class="vjs-no-js">Please enable JavaScript to view videos</p>
                      </video>
                    </div>
                    <div id="editEmptyPreview" class="h-full flex flex-col items-center justify-center">
                      <i class="ti ti-video text-4xl text-gray-600 mb-2"></i>
                      <p class="text-sm text-gray-500">Select a video to preview</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="space-y-2">
              <div class="flex flex-col sm:flex-row sm:items-center">
                <label class="text-sm font-medium text-white">Schedule Settings</label>
                <span id="editServerTimeDisplay"
                  class="mt-1 sm:mt-0 sm:ml-2 block sm:inline-block bg-gray-700 text-xs text-gray-300 px-2 py-0.5 rounded">
                  Server time: loading...
                </span>
              </div>
              <div class="grid grid-cols-1 sm:grid-cols-4 gap-4 sm:items-end">
                <div class="h-[42px] flex items-center justify-between">
                  <label class="text-sm text-gray-300">Loop Video</label>
                  <label class="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" name="loopVideo" id="editLoopVideo" class="sr-only peer" checked>
                    <div class="w-11 h-6 bg-dark-700 rounded-full peer peer-checked:bg-primary"></div>
                    <div
                      class="absolute left-[2px] top-[2px] w-5 h-5 bg-white rounded-full transition-all peer-checked:translate-x-5">
                    </div>
                  </label>
                </div>
                <div>
                  <label for="editScheduleTime" class="text-xs text-gray-400 block mb-1"><%= t('streams.timezone.schedule_time_label') %></label>
                  <input type="datetime-local" id="editScheduleTime" name="scheduleTime"
                    class="w-full h-[42px] px-4 bg-dark-700 border border-gray-600 rounded-lg focus:border-primary focus:ring-1 focus:ring-primary text-sm [color-scheme:dark]">
                  <div id="editScheduleTimeError" class="text-red-400 text-xs mt-1 hidden">Schedule time cannot be in the past</div>
                </div>
                <div>
                  <label for="editDisplayTimezone" class="text-xs text-gray-400 block mb-1">
                    <%= t('streams.timezone.display_in_timezone') %>
                    <span class="text-gray-500 font-normal">(<%= locale === 'en' ? 'for reference only' : 'hanya untuk referensi' %>)</span>
                  </label>
                  <select id="editDisplayTimezone" name="editDisplayTimezone" class="w-full h-[42px] px-4 bg-dark-700 border border-gray-600 rounded-lg focus:border-primary focus:ring-1 focus:ring-primary text-sm">
                    <!-- Options will be populated by JavaScript -->
                  </select>
                </div>
                <div>
                  <label for="editDuration" class="text-xs text-gray-400 block mb-1">Duration (minutes)</label>
                  <input type="number" id="editDuration" min="0" name="duration"
                    class="w-full h-[42px] px-4 bg-dark-700 border border-gray-600 rounded-lg focus:border-primary focus:ring-1 focus:ring-primary text-sm"
                    placeholder="0 = unlimited">
                </div>
              </div>
              <div class="mt-2 text-xs bg-dark-800/80 border border-gray-600/30 p-3 rounded-lg" id="editScheduleHelperText">
                <div class="text-gray-300"><%= t('streams.timezone.helper_text_empty') %></div>
              </div>
            </div>
            <div class="space-y-4">
              <div class="pt-2 border-t border-gray-700">
                <button type="button" id="editAdvancedSettingsToggle"
                  class="flex items-center justify-between w-full text-left">
                  <div class="flex items-center">
                    <span class="text-sm font-medium text-white">Advanced Settings</span>
                    <div class="relative ml-2 group">
                      <i class="ti ti-info-circle text-gray-400 hover:text-primary"></i>
                      <div
                        class="absolute bottom-full left-1/2 -translate-x-1/2 mb-2 hidden group-hover:block w-64 p-2 bg-dark-600 text-xs text-gray-200 rounded-md shadow-lg z-10">
                        <div class="text-center">Menggunakan advanced settings, proses streaming akan menjadi lebih
                          berat karena ada proses re-encoding video</div>
                        <div
                          class="absolute top-full left-1/2 -translate-x-1/2 border-4 border-transparent border-t-dark-600">
                        </div>
                      </div>
                    </div>
                    <span id="editAdvancedSettingsPremiumBadge" class="hidden ml-2 px-2 py-0.5 text-xs bg-yellow-600 text-yellow-100 rounded-full">Premium</span>
                    <!-- Copy Mode Status Indicator for Edit Stream -->
                    <div id="editCopyModeIndicator" class="hidden ml-2 px-2 py-0.5 text-xs bg-green-600 text-green-100 rounded-full">
                      <i class="ti ti-copy text-xs mr-1"></i>Copy Mode
                    </div>
                    <div id="editReencodeIndicator" class="hidden ml-2 px-2 py-0.5 text-xs bg-orange-600 text-orange-100 rounded-full">
                      <i class="ti ti-settings text-xs mr-1"></i>Re-encode
                    </div>
                  </div>
                  <i class="ti ti-chevron-down text-gray-400 transition-transform"></i>
                </button>
                <div id="editAdvancedSettingsContent" class="hidden space-y-6 pt-4">
                  <!-- Premium Plan Required Notice -->
                  <div id="editAdvancedSettingsUpgradeNotice" class="hidden p-4 bg-yellow-600/20 border border-yellow-600/30 rounded-lg">
                    <div class="flex items-start">
                      <div class="flex-shrink-0">
                        <i class="ti ti-crown text-yellow-500 text-lg"></i>
                      </div>
                      <div class="ml-3">
                        <h4 class="text-sm font-medium text-yellow-400">Advanced Settings - Premium Feature</h4>
                        <p id="editAdvancedSettingsUpgradeMessage" class="text-sm text-gray-300 mt-1">Advanced settings hanya tersedia di plan premium. Upgrade untuk mengakses fitur re-encoding video berkualitas tinggi.</p>
                        <div class="mt-3">
                          <a href="/subscription/plans" class="inline-flex items-center px-3 py-1.5 text-xs font-medium bg-yellow-600 hover:bg-yellow-700 text-white rounded-md transition-colors">
                            <i class="ti ti-arrow-up-right text-xs mr-1"></i>
                            Upgrade Plan
                          </a>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="space-y-4">
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <div>
                        <label class="text-xs text-gray-400 block mb-1">Bitrate</label>
                        <select name="bitrate" id="editBitrate"
                          class="w-full h-[42px] px-4 bg-dark-700 border border-gray-600 rounded-lg focus:border-primary focus:ring-1 focus:ring-primary text-sm">
                          <option value="2500">2500 kbps</option>
                          <option value="4000">4000 kbps</option>
                          <option value="6000">6000 kbps</option>
                          <option value="8000">8000 kbps</option>
                          <option value="10000">10000 kbps</option>
                          <option value="12000">12000 kbps</option>
                          <option value="15000">15000 kbps</option>
                          <option value="20000">20000 kbps</option>
                        </select>
                      </div>
                      <div>
                        <label class="text-xs text-gray-400 block mb-1">Frame Rate</label>
                        <select name="fps" id="editFps"
                          class="w-full h-[42px] px-4 bg-dark-700 border border-gray-600 rounded-lg focus:border-primary focus:ring-1 focus:ring-primary text-sm">
                          <option value="30">30 FPS</option>
                          <option value="60">60 FPS</option>
                          <option value="120">120 FPS</option>
                        </select>
                      </div>
                      <div>
                        <label class="text-xs text-gray-400 block mb-1">Resolution</label>
                        <select id="editResolutionSelect"
                          class="w-full h-[42px] px-4 bg-dark-700 border border-gray-600 rounded-lg focus:border-primary focus:ring-1 focus:ring-primary text-sm">
                          <option value="720" selected data-horizontal="1280x720" data-vertical="720x1280">720p HD
                          </option>
                          <option value="1080" data-horizontal="1920x1080" data-vertical="1080x1920">1080p Full HD
                          </option>
                          <option value="1440" data-horizontal="2560x1440" data-vertical="1440x2560">1440p QHD</option>
                          <option value="2160" data-horizontal="3840x2160" data-vertical="2160x3840">2160p 4K</option>
                        </select>
                        <div class="text-xs text-gray-500 mt-1">
                          <span id="editCurrentResolution">1280x720</span>
                        </div>
                      </div>
                      <div>
                        <label class="text-xs text-gray-400 block mb-1">Orientation</label>
                        <div class="flex gap-2 h-[42px]">
                          <button type="button" onclick="setEditVideoOrientation('horizontal')"
                            class="flex-1 flex items-center justify-center bg-dark-700 hover:bg-dark-600 border border-gray-600 rounded-lg transition-colors active-orientation">
                            <i class="ti ti-rectangle text-sm mr-1"></i>
                            <span class="text-xs">Landscape</span>
                          </button>
                          <button type="button" onclick="setEditVideoOrientation('vertical')"
                            class="flex-1 flex items-center justify-center bg-dark-700 hover:bg-dark-600 border border-gray-600 rounded-lg transition-colors">
                            <i class="ti ti-rectangle-vertical text-sm mr-1"></i>
                            <span class="text-xs">Portrait</span>
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </form>
        </div>
        <div class="flex-shrink-0 flex items-center justify-end gap-3 p-4 sm:px-6 sm:py-6 border-t border-gray-700">
          <button onclick="closeEditStreamModal()"
            class="px-5 py-2.5 text-sm font-medium text-gray-300 hover:text-white transition-colors">
            Cancel
          </button>
          <button type="submit" form="editStreamForm"
            class="px-5 py-2.5 text-sm font-medium bg-primary hover:bg-secondary text-white rounded-lg transition-colors">
            Save Changes
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Upgrade to Stream Modal -->
  <div id="upgradeStreamModal" class="fixed inset-0 bg-black/50 z-50 hidden modal-overlay overflow-y-auto">
    <div class="flex min-h-screen items-center justify-center p-4">
      <div class="bg-dark-800 rounded-lg shadow-xl w-full max-w-md modal-container flex flex-col">
        <div class="flex-shrink-0 flex items-center justify-between p-4 sm:px-6 sm:py-6 border-b border-gray-700">
          <h3 class="text-lg font-semibold"><%= t('dashboard.upgrade_modal.title') %></h3>
          <button onclick="closeUpgradeModal()" class="text-gray-400 hover:text-white">
            <i class="ti ti-x text-xl"></i>
          </button>
        </div>
        <div class="p-6 text-center">
          <p class="text-gray-300 mb-6"><%= t('dashboard.upgrade_modal.message') %></p>
        </div>
        <div class="flex-shrink-0 flex items-center justify-center gap-3 p-4 sm:px-6 sm:py-6 border-t border-gray-700">
          <a href="/subscription/plans" class="btn-primary-enhanced w-full flex items-center justify-center gap-2">
            <%= t('dashboard.upgrade_modal.cta') %>
          </a>
        </div>
      </div>
    </div>
  </div>

  <style>
    .video-js {
      background-color: #0d0d0e;
      width: 100%;
      height: 100%;
    }
    .video-js .vjs-big-play-button {
      background-color: rgba(59, 130, 246, 0.7);
      border-color: rgba(59, 130, 246, 0.7);
      border-radius: 50%;
      width: 60px;
      height: 60px;
      line-height: 60px;
      left: 50%;
      top: 50%;
      margin-left: -30px;
      margin-top: -30px;
    }
    .video-js:hover .vjs-big-play-button {
      background-color: rgb(59, 130, 246);
    }
    .video-js .vjs-control-bar {
      background-color: rgba(31, 41, 55, 0.7);
    }
    .video-js .vjs-slider {
      background-color: rgba(255, 255, 255, 0.2);
    }
    .video-js .vjs-play-progress,
    .video-js .vjs-volume-level {
      background-color: rgb(59, 130, 246);
    }
    .video-js .vjs-load-progress {
      background: rgba(255, 255, 255, 0.3);
    }
    .aspect-video .video-js {
      width: 100% !important;
      height: 100% !important;
      position: relative !important;
      background: #000 !important;
    }
    .video-js .vjs-control-bar {
      display: flex !important;
      visibility: visible !important;
      opacity: 1 !important;
      transition: visibility 0.1s, opacity 0.1s !important;
    }
    .video-js.vjs-user-inactive:not(.vjs-paused) .vjs-control-bar {
      visibility: visible !important;
      opacity: 0.7 !important;
    }
    .video-js .vjs-progress-control {
      min-width: 4em !important;
      flex: 1 !important;
    }
    .video-js .vjs-progress-control .vjs-progress-holder {
      margin: 0 10px !important;
      height: 0.3em !important;
    }
    .video-js .vjs-progress-control:hover .vjs-progress-holder {
      height: 0.5em !important;
    }
    .vjs-icon-placeholder:before {
      position: absolute !important;
      top: 50% !important;
      left: 50% !important;
      transform: translate(-50%, -50%) !important;
    }
    #videoListContainer {
      scrollbar-width: thin;
      scrollbar-color: rgba(156, 163, 175, 0.3) transparent;
    }
    #videoListContainer::-webkit-scrollbar {
      width: 6px;
    }
    #videoListContainer::-webkit-scrollbar-track {
      background: transparent;
    }
    #videoListContainer::-webkit-scrollbar-thumb {
      background-color: rgba(156, 163, 175, 0.3);
      border-radius: 20px;
    }
    #videoSearchInput::placeholder {
      color: rgba(156, 163, 175, 0.7);
    }
    .highlight {
      background-color: rgba(59, 130, 246, 0.2);
      padding: 0 2px;
      border-radius: 2px;
    }
    #platformDropdown {
      transform-origin: top right;
      transition: transform 0.2s, opacity 0.2s;
      transform: scale(0.95);
      opacity: 0;
    }
    #platformDropdown:not(.hidden) {
      transform: scale(1);
      opacity: 1;
    }
    .platform-option:hover i {
      transform: scale(1.1);
    }
    .platform-option i {
      transition: transform 0.2s;
    }
    #platformSelector {
      display: flex;
      align-items: center;
      justify-content: center;
    }
    #platformSelector i {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      font-size: 1.25rem;
      line-height: 1;
    }
    #platformDropdown {
      transform-origin: top right;
      transition: transform 0.2s, opacity 0.2s;
      transform: scale(0.95);
      opacity: 0;
    }
    #platformDropdown:not(.hidden) {
      transform: scale(1);
      opacity: 1;
    }
    * {
      scrollbar-width: thin;
      scrollbar-color: rgba(156, 163, 175, 0.3) transparent;
    }
    ::-webkit-scrollbar {
      width: 5px;
      height: 5px;
    }
    ::-webkit-scrollbar-track {
      background: transparent;
    }
    ::-webkit-scrollbar-thumb {
      background-color: rgba(156, 163, 175, 0.3);
      border-radius: 20px;
    }
    ::-webkit-scrollbar-thumb:hover {
      background-color: rgba(156, 163, 175, 0.5);
    }
    .modal-overlay,
    .overflow-y-auto,
    .overflow-x-auto {
      scrollbar-width: thin;
      scrollbar-color: rgba(156, 163, 175, 0.3) transparent;
    }
    .modal-overlay::-webkit-scrollbar,
    .overflow-y-auto::-webkit-scrollbar,
    .overflow-x-auto::-webkit-scrollbar {
      width: 5px;
      height: 5px;
    }
    .bg-dark-800::-webkit-scrollbar-thumb {
      background-color: rgba(156, 163, 175, 0.2);
    }
    .group:hover .group-hover\:block {
      display: block;
    }
    .group .group-hover\:block {
      transition-delay: 200ms;
    }
    .group:hover .group-hover\:block {
      animation: fadeIn 0.2s;
    }
    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: translate(-50%, 5px);
      }
      to {
        opacity: 1;
        transform: translate(-50%, 0);
      }
    }

    /* Desktop Stream Cards Styling */
    #desktop-streams-grid {
      animation: fadeIn 0.3s ease-in-out;
    }

    #desktop-streams-grid .bg-dark-800 {
      background: linear-gradient(135deg, #1a1a1a 0%, #0f0f0f 100%);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    #desktop-streams-grid .bg-dark-800:hover {
      border-color: rgba(173, 102, 16, 0.4) !important;
      box-shadow: 0 10px 20px -5px rgba(0, 0, 0, 0.4), 0 4px 8px -2px rgba(0, 0, 0, 0.2);
      transform: translateY(-1px);
    }

    #desktop-streams-grid img {
      transition: transform 0.3s ease;
    }

    #desktop-streams-grid .bg-dark-800:hover img {
      transform: scale(1.02);
    }

    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: translateY(10px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    /* Responsive grid adjustments */
    @media (min-width: 768px) and (max-width: 1023px) {
      #desktop-streams-grid {
        grid-template-columns: repeat(2, 1fr);
      }
    }

    @media (min-width: 1024px) and (max-width: 1279px) {
      #desktop-streams-grid {
        grid-template-columns: repeat(2, 1fr);
      }
    }

    @media (min-width: 1280px) {
      #desktop-streams-grid {
        grid-template-columns: repeat(3, 1fr);
      }
    }

    /* Action buttons in cards */
    #desktop-streams-grid .btn-primary-enhanced,
    #desktop-streams-grid button[class*="bg-"] {
      font-size: 0.75rem;
      padding: 0.5rem 1rem;
      font-weight: 500;
    }

    /* Fix video preview zoom issues */
    .video-js video {
      object-fit: contain !important;
      width: 100% !important;
      height: 100% !important;
      background: #000 !important;
    }

    .video-js .vjs-tech {
      object-fit: contain !important;
      width: 100% !important;
      height: 100% !important;
      background: #000 !important;
      position: relative !important;
    }

    /* Ensure video is visible */
    .video-js .vjs-tech video {
      display: block !important;
      visibility: visible !important;
      opacity: 1 !important;
    }

    /* Video preview containers */
    #videoPreview,
    #videoPreviewMobile,
    #editVideoPreview,
    #editVideoPreviewMobile {
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      z-index: 10;
    }

    /* When video preview is shown, ensure it's visible */
    #videoPreview:not(.hidden),
    #videoPreviewMobile:not(.hidden),
    #editVideoPreview:not(.hidden),
    #editVideoPreviewMobile:not(.hidden) {
      background: transparent;
    }

    /* Ensure video containers maintain proper aspect ratio */
    .aspect-video {
      position: relative;
      overflow: hidden;
    }

    /* Empty preview states */
    #emptyPreview,
    #emptyPreviewMobile,
    #editEmptyPreview,
    #editEmptyPreviewMobile {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }

    /* Modal Specific Styles - Force visibility */
    #editStreamModal,
    #newStreamModal {
      position: fixed !important;
      top: 0 !important;
      left: 0 !important;
      right: 0 !important;
      bottom: 0 !important;
      z-index: 50000 !important;
      background: rgba(0, 0, 0, 0.5) !important;
      align-items: center !important;
      justify-content: center !important;
    }

    #editStreamModal.hidden,
    #newStreamModal.hidden {
      display: none !important;
    }

    #editStreamModal:not(.hidden),
    #newStreamModal:not(.hidden) {
      display: flex !important;
    }

    /* Ensure modal content is visible */
    #editStreamModal .bg-dark-800,
    #newStreamModal .bg-dark-800 {
      background: #1f2937 !important;
      border-radius: 12px !important;
      box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5) !important;
      max-width: 90vw !important;
      max-height: 90vh !important;
      overflow: auto !important;
    }

    /* Force all modal overlays to work */
    .modal-overlay {
      position: fixed !important;
      top: 0 !important;
      left: 0 !important;
      right: 0 !important;
      bottom: 0 !important;
      z-index: 50000 !important;
    }

    .modal-overlay:not(.hidden) {
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
    }

    .modal-overlay.hidden {
      display: none !important;
    }

    /* Mobile-specific fixes for video preview and dropdown positioning */
    @media (max-width: 768px) {
      /* Fix mobile video preview positioning to not obstruct dropdowns */
      #videoPreviewMobile,
      #editVideoPreviewMobile {
        position: relative;
        z-index: 1;
      }

      /* Ensure video selector dropdowns appear above video previews */
      #videoSelectorDropdown,
      #editVideoSelectorDropdown {
        z-index: 70 !important;
        position: absolute !important;
      }

      /* Adjust mobile modal layout for better interaction */
      .lg\\:grid-cols-2 {
        grid-template-columns: 1fr;
        gap: 1rem;
      }

      /* Ensure mobile video preview doesn't interfere with form elements */
      .lg\\:hidden .aspect-video {
        margin-bottom: 1rem;
        order: -1; /* Move video preview to top on mobile */
      }

      /* Modal container adjustments for mobile */
      .modal-container {
        margin: 1rem;
        max-height: calc(100vh - 2rem);
        overflow-y: auto;
      }

      /* Grid adjustments for mobile */
      .grid-cols-2 {
        grid-template-columns: 1fr;
      }

      .grid-cols-3 {
        grid-template-columns: repeat(2, 1fr);
      }

      /* Form input adjustments for mobile */
      .modal-form-group {
        margin-bottom: 1rem;
      }

      .modal-input {
        padding: 0.75rem;
        font-size: 16px; /* Prevent zoom on iOS */
      }

      /* Mobile dashboard improvements */
      #streams-grid {
        grid-template-columns: 1fr !important;
        gap: 1rem !important;
      }

      /* Mobile stats cards improvements */
      .card-enhanced {
        padding: 1rem !important;
      }

      /* Mobile stream card improvements */
      .card-enhanced .aspect-video {
        border-radius: 0.5rem;
      }

      /* Mobile button improvements */
      .btn-primary-enhanced {
        padding: 0.75rem 1rem;
        font-size: 0.875rem;
      }

      /* Mobile search input */
      .relative input[type="text"] {
        font-size: 16px; /* Prevent zoom on iOS */
      }
    }
  </style>
  <script>
    // stream-modal.min.js is already loaded from layout.ejs with defer
    // Wait for it to be available
    let streamModalReady = false;

    function waitForStreamModal() {
      if (typeof window.openNewStreamModal !== 'undefined' && !streamModalReady) {
        console.log('✅ stream-modal functions are available');
        streamModalReady = true;
        // Remove fallback function now that real one is available
        if (window.openNewStreamModalFallback) {
          delete window.openNewStreamModalFallback;
        }
        return;
      }
      if (!streamModalReady) {
        console.log('⏳ Waiting for stream-modal functions...');
        setTimeout(waitForStreamModal, 100);
      }
    }

    // Start waiting when DOM is ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', waitForStreamModal);
    } else {
      waitForStreamModal();
    }
  </script>
  <script>
    // System stats removed - now showing slot and storage info instead
    // Stream creation form handling is now managed by enhanced-stream-modal.js
    // This ensures consistent notification system usage across all stream operations


    // Check slot availability and update button state
    async function checkSlotAvailabilityAndOpenModal() {
      // Show loading state immediately
      const createBtn = document.getElementById('newStreamBtn');
      let originalText = 'Create Stream';
      if (createBtn) {
        originalText = createBtn.querySelector('span')?.textContent || 'Create Stream';
        createBtn.disabled = true;
        const btnText = createBtn.querySelector('span');
        if (btnText) {
          btnText.textContent = 'Checking...';
        }
      }
      
      try {
        const response = await fetch('/subscription/quota');
        const data = await response.json();
        if (data.streaming) {
          const { streaming } = data;

          // Check if user has available slots
          if (streaming.max === 0) {
            // Preview plan - redirect to plans page
            window.location.href = '/subscription/plans';
            return;
          } else if (streaming.max !== -1 && streaming.current >= streaming.max) {
            // No slots available
            const message = `You have reached your streaming limit of ${streaming.max} concurrent streams. Please upgrade your plan or stop an existing stream to create a new one.`;
            if (typeof notifications !== 'undefined') {
              notifications.warning('Streaming Limit Reached', message);
            } else {
              alert(message);
            }
            return;
          }

          // Slots available, open modal
          openNewStreamModal();
        } else {
          console.error('Failed to check quota:', data.error);
          // Fallback: open modal anyway
          openNewStreamModal();
        }
      } catch (error) {
        console.error('Error checking slot availability:', error);
        // Fallback: open modal anyway
        openNewStreamModal();
      } finally {
        // Restore button state
        if (createBtn) {
          createBtn.disabled = false;
          const btnText = createBtn.querySelector('span');
          if (btnText) {
            btnText.textContent = originalText;
          }
        }
      }
    }

    // Make this function globally available
    window.checkSlotAvailabilityAndOpenModal = checkSlotAvailabilityAndOpenModal;

    // Provide temporary fallback function
    if (typeof window.openNewStreamModal === 'undefined') {
      console.warn('openNewStreamModal not yet available, providing fallback...');
      // Temporary fallback function until stream-modal.js loads
      window.openNewStreamModalFallback = function() {
        console.log('Fallback openNewStreamModal called');
        const modal = document.getElementById('newStreamModal');
        if (modal) {
          modal.classList.remove('hidden');
          modal.style.display = 'flex';
          document.body.style.overflow = 'hidden';
        }
      };
      // Use fallback temporarily
      window.openNewStreamModal = window.openNewStreamModalFallback;
    }

    // Update main action button state based on slot availability
    async function updateMainActionButtonState() {
      try {
        const response = await fetch('/subscription/quota');
        const data = await response.json();

        if (data.streaming) {
          const { streaming } = data;
          const button = document.getElementById('main-action-button');
          const buttonText = document.getElementById('main-action-text');
          const buttonIcon = document.getElementById('main-action-icon');

          if (streaming.max === 0) {
            // Preview plan - change to "Buy Plan" button
            if (buttonText) buttonText.textContent = '<%= t("dashboard.buy_plan") %>';
            if (buttonIcon) {
              buttonIcon.className = 'ti ti-shopping-cart';
            }
            button.title = '<%= t("subscription.upgrade_plan") %>';
          } else if (streaming.max !== -1 && streaming.current >= streaming.max) {
            // No slots available - show upgrade option
            if (buttonText) buttonText.textContent = '<%= t("dashboard.upgrade_plan") %>';
            if (buttonIcon) {
              buttonIcon.className = 'ti ti-arrow-up';
            }
            button.title = `You have reached your streaming limit of ${streaming.max} concurrent streams. Upgrade to get more slots.`;
          } else {
            // Slots available - show create stream
            if (buttonText) buttonText.textContent = '<%= t("dashboard.create_stream") %>';
            if (buttonIcon) {
              buttonIcon.className = 'ti ti-plus';
            }
            button.title = 'Create a new stream';
          }
        }

        // Also update the header button if it exists
        const headerButton = document.getElementById('newStreamBtn');
        if (headerButton) {
          const headerButtonText = headerButton.querySelector('span');

          if (data.streaming && data.streaming.max === 0) {
            // Preview plan - change button to open upgrade modal
            headerButton.disabled = false;
            headerButton.classList.remove('disabled:bg-gray-600', 'disabled:cursor-not-allowed');
            headerButton.classList.add('hover:bg-secondary');
            if (headerButtonText) headerButtonText.textContent = '<%= t("dashboard.upgrade_to_stream") %>';
            headerButton.title = '<%= t("dashboard.upgrade_tooltip") %>';
            headerButton.onclick = openUpgradeModal;
          } else if (data.streaming && data.streaming.max !== -1 && data.streaming.current >= data.streaming.max) {
            // No slots available - disable button
            headerButton.disabled = true;
            headerButton.classList.add('disabled:bg-gray-600', 'disabled:cursor-not-allowed');
            headerButton.classList.remove('hover:bg-secondary');
            if (headerButtonText) headerButtonText.textContent = `<%= t('dashboard.no_slots_available') %> (${data.streaming.current}/${data.streaming.max})`;
            headerButton.title = `<%= t('dashboard.streaming_limit_reached', { max: data.streaming.max }) %>`;
          } else if (data.streaming) {
            // Slots available - enable button
            headerButton.disabled = false;
            headerButton.classList.remove('disabled:bg-gray-600', 'disabled:cursor-not-allowed');
            headerButton.classList.add('hover:bg-secondary');
            headerButton.setAttribute('onclick', 'checkSlotAvailabilityAndOpenModal()'); // Restore original onclick

            if (data.streaming.max === -1) {
              if (headerButtonText) headerButtonText.textContent = `<%= t('dashboard.new_stream') %> (${data.streaming.current}/∞)`;
            } else {
              if (headerButtonText) headerButtonText.textContent = `<%= t('dashboard.new_stream') %> (${data.streaming.current}/${data.streaming.max})`;
            }

            // Update tooltip with more info
            if (data.streaming.max === -1) {
              headerButton.title = '<%= t("dashboard.create_stream_unlimited") %>';
            } else {
              headerButton.title = `<%= t('dashboard.create_stream_slots', { available: data.streaming.available }) %>`;
            }
          }
        }
      } catch (error) {
        // console.error('Error updating button state:', error); // Cleaned for production
      }
    }

    // Keep the old function name for backward compatibility
    const updateNewStreamButtonState = updateMainActionButtonState;

    // Check if user can access advanced settings based on plan price
    async function checkAdvancedSettingsAccess() {
      try {
        // console.log('🔍 Checking advanced settings access...'); // Cleaned for production
        // console.log('🚀 Function checkAdvancedSettingsAccess() // Cleaned for production called');

        // Get quota info and eligible plans
        const [quotaResponse, eligiblePlansResponse] = await Promise.all([
          fetch('/subscription/quota'),
          fetch('/subscription/advanced-eligible-plans')
        ]);

        const data = await quotaResponse.json();
        const eligiblePlansData = await eligiblePlansResponse.json();

        // console.log('📊 Quota API response:', data); // Cleaned for production
        // console.log('📊 Quota response status:', quotaResponse.status); // Cleaned for production
        // console.log('📊 Eligible plans response:', eligiblePlansData); // Cleaned for production
        // console.log('📊 Data.success value:', data.success); // Cleaned for production
        // console.log('📊 Data.success type:', typeof data.success); // Cleaned for production
        // console.log('📊 Full response object:', JSON.stringify(data, null, 2) // Cleaned for production);

        if (data.success) {
          // Check if user is admin first
          // console.log('🔍 Fetching user profile...'); // Cleaned for production
          const userResponse = await fetch('/api/user/profile');
          const userData = await userResponse.json();

          // console.log('👤 User data:', userData); // Cleaned for production
          // console.log('👤 User response status:', userResponse.status); // Cleaned for production

          const isAdmin = userData.success && userData.user && userData.user.role === 'admin';
          // console.log('🔑 Is admin check:', isAdmin); // Cleaned for production

          if (isAdmin) {
            // console.log('👑 User is admin - Advanced settings always enabled'); // Cleaned for production
            return; // Admin users have unlimited access
          }

          // For non-admin users, check plan price
          // console.log('📋 Checking plan data:', data.plan); // Cleaned for production
          if (data.plan) {
            const planPrice = parseFloat(data.plan.price) || 0;
            const hasAdvancedAccess = planPrice >= 49900;

            // console.log(`💰 Plan: ${data.plan.name}, Price: Rp. ${planPrice.toLocaleString('id-ID') // Cleaned for production}`);
            // console.log(`🎯 Advanced access: ${hasAdvancedAccess ? 'ALLOWED' : 'RESTRICTED'}`); // Cleaned for production

            // Control create stream advanced settings
            const createAdvancedToggle = document.getElementById('advancedSettingsToggle');
            const createAdvancedContent = document.getElementById('advancedSettingsContent');
            const createUpgradeNotice = document.getElementById('advancedSettingsUpgradeNotice');
            const createPremiumBadge = document.getElementById('advancedSettingsPremiumBadge');

            // Control edit stream advanced settings
            const editAdvancedToggle = document.getElementById('editAdvancedSettingsToggle');
            const editAdvancedContent = document.getElementById('editAdvancedSettingsContent');
            const editUpgradeNotice = document.getElementById('editAdvancedSettingsUpgradeNotice');
            const editPremiumBadge = document.getElementById('editAdvancedSettingsPremiumBadge');

            if (!hasAdvancedAccess) {
              // console.log('🚫 Restricting advanced settings access'); // Cleaned for production

              // Update dynamic messages
              const eligiblePlans = eligiblePlansData.success ? eligiblePlansData.plans : [];
              let upgradeMessage = 'Advanced settings hanya tersedia di plan premium. Upgrade untuk mengakses fitur re-encoding video berkualitas tinggi.';

              if (eligiblePlans.length > 0) {
                const planNames = eligiblePlans.map(plan => plan.name).join(', ');
                upgradeMessage = `Advanced settings hanya tersedia di plan ${planNames}. Upgrade untuk mengakses fitur re-encoding video berkualitas tinggi.`;
              }

              // console.log('📝 Dynamic upgrade message:', upgradeMessage); // Cleaned for production

              // Update message elements
              const createUpgradeMessage = document.getElementById('advancedSettingsUpgradeMessage');
              const editUpgradeMessage = document.getElementById('editAdvancedSettingsUpgradeMessage');
              if (createUpgradeMessage) createUpgradeMessage.textContent = upgradeMessage;
              if (editUpgradeMessage) editUpgradeMessage.textContent = upgradeMessage;

              // Show premium badges
              if (createPremiumBadge) createPremiumBadge.classList.remove('hidden');
              if (editPremiumBadge) editPremiumBadge.classList.remove('hidden');

              // Disable advanced settings toggles
              if (createAdvancedToggle) {
                createAdvancedToggle.style.opacity = '0.6';
                createAdvancedToggle.style.cursor = 'not-allowed';
                createAdvancedToggle.onclick = function(e) {
                  e.preventDefault();
                  if (createUpgradeNotice) {
                    createUpgradeNotice.classList.remove('hidden');
                    createAdvancedContent.classList.remove('hidden');
                  }
                };
              }

              if (editAdvancedToggle) {
                editAdvancedToggle.style.opacity = '0.6';
                editAdvancedToggle.style.cursor = 'not-allowed';
                editAdvancedToggle.onclick = function(e) {
                  e.preventDefault();
                  if (editUpgradeNotice) {
                    editUpgradeNotice.classList.remove('hidden');
                    editAdvancedContent.classList.remove('hidden');
                  }
                };
              }

              // Disable all advanced settings inputs
              const advancedInputs = document.querySelectorAll('#advancedSettingsContent input, #advancedSettingsContent select, #editAdvancedSettingsContent input, #editAdvancedSettingsContent select');
              advancedInputs.forEach(input => {
                input.disabled = true;
                input.style.opacity = '0.5';
                input.style.cursor = 'not-allowed';
              });
            } else {
              // console.log('✅ Advanced settings enabled'); // Cleaned for production
            }
          } else {
            // console.log('⚠️ No plan data found - restricting access'); // Cleaned for production
            // No plan = restrict access
            const createPremiumBadge = document.getElementById('advancedSettingsPremiumBadge');
            const editPremiumBadge = document.getElementById('editAdvancedSettingsPremiumBadge');
            if (createPremiumBadge) createPremiumBadge.classList.remove('hidden');
            if (editPremiumBadge) editPremiumBadge.classList.remove('hidden');
          }
        } else {
          // console.log('❌ Quota API returned success: false'); // Cleaned for production
        }
      } catch (error) {
        // console.error('❌ Error checking advanced settings access:', error); // Cleaned for production
        // console.error('❌ Error details:', error.message); // Cleaned for production
        // console.error('❌ Error stack:', error.stack); // Cleaned for production
      }
    }

    document.addEventListener('DOMContentLoaded', function () {
      const isProduction = typeof window !== 'undefined' && window.location.hostname !== 'localhost';

      // Load streams with real-time status
      loadStreamsWithStatus();

      // Update button state initially
      updateMainActionButtonState();

      // Initialize quota display
      updateQuotaDisplay();

      // Listen for storage update events
      window.addEventListener('storageUpdated', function() {
        updateQuotaDisplay();
      });

      // Check advanced settings access
      checkAdvancedSettingsAccess();

    // Initialize copy mode compatibility checking
    initializeCopyModeCompatibility();

    // Initialize resolution and orientation handlers
    initializeResolutionHandlers();

      // Refresh stream status and button state every 20 seconds for balanced accuracy
      setInterval(() => {
        loadStreamsWithStatus();
        updateMainActionButtonState();
        updateQuotaDisplay(); // Add quota refresh
      }, 20000);

      // Fallback: also try regular API after 2 seconds if no streams shown
      setTimeout(() => {
        const tableBody = document.querySelector('table tbody');
        const visibleRows = tableBody ? tableBody.querySelectorAll('tr:not(#empty-state):not([style*="display: none"])') : [];
        if (visibleRows.length === 0) {
          // console.log('No streams visible after 2 seconds, trying fallback...'); // Cleaned for production // Debug log
          loadStreamsRegular();
        }
      }, 2000);
    });

    // Add debouncing to prevent multiple simultaneous API calls
    let isLoadingStreams = false;

    function loadStreamsWithStatus() {
      if (isLoadingStreams) {
        return; // Skip if already loading
      }

      isLoadingStreams = true;

      fetch('/api/streams/status', {
        credentials: 'same-origin', // Ensure cookies are sent
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      })
        .then(response => {
          if (!response.ok) {
            if (response.status === 401 || response.status === 403) {
              // Session expired, redirect to login
              window.location.href = '/login';
              return;
            }
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }
          return response.json();
        })
        .then(data => {
          if (data.success) {
            displayStreams(data.streams);
            updateStreamCounters(data.streams);
          } else {
            // Fallback to regular streams API
            loadStreamsRegular();
          }
        })
        .catch(error => {
          // Check if it's an authentication error
          if (error.message.includes('401') || error.message.includes('403')) {
            // Session expired, redirect to login
            window.location.href = '/login';
            return;
          }
          // Fallback to regular streams API
          loadStreamsRegular();
        })
        .finally(() => {
          isLoadingStreams = false;
        });
    }

    function loadStreamsRegular() {
      fetch('/api/streams', {
        credentials: 'same-origin',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      })
        .then(response => {
          if (!response.ok) {
            if (response.status === 401 || response.status === 403) {
              window.location.href = '/login';
              return;
            }
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }
          return response.json();
        })
        .then(data => {
          if (data.success) {
            displayStreams(data.streams);
            updateStreamCounters(data.streams);
          } else {
            showEmptyState();
          }
        })
        .catch(error => {
          showEmptyState();
        });
    }
    function updateStreamCounters(streams) {
      const liveStreams = streams.filter(stream => stream.status === 'live' || stream.isReallyActive).length;
      const totalStreams = streams.length;

      // Update counter using specific ID (works for both mobile and desktop now)
      const counter = document.getElementById('active-streams-count');
      if (counter) {
        counter.textContent = liveStreams;
      }

      // Update status text using specific ID to avoid affecting language dropdown
      const statusText = document.getElementById('active-streams-status');
      if (statusText) {
        statusText.textContent = liveStreams === 0 ? 'No active streams' :
          liveStreams === 1 ? '1 active stream' :
            `${liveStreams} active streams`;
      }

      // Update streaming quota display
      const slotsUsedElement = document.getElementById('slots-used');
      const slotsBarElement = document.getElementById('slots-bar');

      if (slotsUsedElement) {
        slotsUsedElement.textContent = totalStreams; // Show total streams, not just live ones
      }

      // Update quota bar if we have max slots info
      if (slotsBarElement && window.quotaInfo && window.quotaInfo.streaming) {
        const maxSlots = window.quotaInfo.streaming.max;
        if (maxSlots !== -1) {
          const percentage = (totalStreams / maxSlots) * 100;
          slotsBarElement.style.width = `${Math.min(percentage, 100)}%`;
        }
      }

      // console.log(`Updated counters: ${liveStreams} live streams, ${totalStreams} total streams`); // Cleaned for production // Debug log
    }

    // Function to update quota display
    function updateQuotaDisplay() {
      fetch('/subscription/quota')
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            // Update streaming quota
            const slotsUsedElement = document.getElementById('slots-used');
            const slotsBarElement = document.getElementById('slots-bar');

            if (slotsUsedElement && data.streaming) {
              slotsUsedElement.textContent = data.streaming.current;

              // Update quota bar
              if (slotsBarElement && data.streaming.max !== -1) {
                const percentage = (data.streaming.current / data.streaming.max) * 100;
                slotsBarElement.style.width = `${Math.min(percentage, 100)}%`;
              }
            }

            // Update storage display
            const storageUsedElement = document.getElementById('storage-used');
            const storageBarElement = document.getElementById('storage-bar');

            if (storageUsedElement && data.storage) {
              storageUsedElement.textContent = `${data.storage.current}${data.storage.unit}`;

              // Update storage bar
              if (storageBarElement) {
                storageBarElement.style.width = `${data.storage.percentage}%`;
              }
            }

            // Store quota info globally for other functions
            window.quotaInfo = data;
          }
        })
        .catch(error => {
          // console.error('Error updating quota display:', error); // Cleaned for production
        });
    }
      function displayStreams(streams) {
        // console.log('displayStreams called with:', streams); // Cleaned for production // Debug log
        if (!streams || streams.length === 0) {
          // console.log('No streams to display, showing empty state'); // Cleaned for production // Debug log
          showEmptyState();
          return;
        }
        // console.log('Displaying', streams.length, 'streams'); // Cleaned for production // Debug log

        // Display streams using unified function
        try {
          displayStreams(streams);
        } catch (error) {
          // console.error('Error displaying streams:', error); // Cleaned for production
        }

        // Fallback: simple table insertion
        if (!document.querySelector('tr[data-stream-id]')) {
          // console.log('Fallback: using simple table insertion'); // Cleaned for production // Debug log
          displayStreamsSimple(streams);
        }
      }

      function displayStreamsSimple(streams) {
        // Try to use the new unified cards system first
        try {
          displayStreams(streams);
          return;
        } catch (error) {
          // console.error('Error with cards display, falling back to simple display'); // Cleaned for production
        }

        // Fallback: show empty state if no streams
        if (!streams || streams.length === 0) {
          showEmptyState();
          return;
        }

        // If cards system fails, just ensure empty state is hidden
        const desktopGrid = document.getElementById('desktop-streams-grid');
        const desktopEmptyState = document.getElementById('desktop-empty-state');
        if (desktopGrid && desktopEmptyState) {
          desktopGrid.style.display = 'grid';
          desktopEmptyState.style.display = 'none';

          // Try to populate cards manually
          desktopGrid.innerHTML = '';
          streams.forEach(stream => {
            try {
              const card = createDesktopStreamCard(stream);
              desktopGrid.appendChild(card);
            } catch (error) {
              // console.error('Error creating card for stream:', stream.id); // Cleaned for production
            }
          });
        }

        // console.log(`Simple display: Processed ${streams.length} streams`); // Cleaned for production
      }
      function displayStreams(streams) {
        const streamsGrid = document.getElementById('streams-grid');
        const emptyState = document.getElementById('streams-empty-state');

        if (!streamsGrid) {
          // console.error('Streams grid not found!'); // Cleaned for production
          return;
        }

        // Clear existing cards
        streamsGrid.innerHTML = '';

        if (streams.length === 0) {
          streamsGrid.style.display = 'none';
          emptyState.style.display = 'block';
          return;
        }

        streamsGrid.style.display = 'grid';
        emptyState.style.display = 'none';

        streams.forEach(stream => {
          const card = createStreamCard(stream);
          streamsGrid.appendChild(card);
        });
      }


      function createStreamCard(stream) {
        const card = document.createElement('div');
        card.className = 'bg-dark-800 border border-gray-700 rounded-lg overflow-hidden hover:shadow-lg transition-all duration-300 hover:border-primary/30';
        card.dataset.streamId = stream.id;

        const thumbnail = stream.video_thumbnail ?
          stream.video_thumbnail :
          'https://via.placeholder.com/400x225?text=No+Preview';

        // Use enhanced status validation with running detection
        let actualStatus = stream.status;
        let isStreamRunning = false;

        if (stream.status === 'live' && !stream.isReallyActive) {
          actualStatus = 'inconsistent';
        } else if (stream.isReallyActive) {
          actualStatus = 'live';
          isStreamRunning = true;
        }

        // Additional check: if stream is in memory (active) regardless of DB status
        if (stream.isReallyActive) {
          isStreamRunning = true;
        }

        const statusBadge = getStatusBadgeHTML(actualStatus);
        const startDate = stream.start_time ? formatDate(new Date(stream.start_time)) : '';
        // Use timezone-aware formatting for scheduled streams
        const scheduleDate = stream.schedule_time && stream.schedule_timezone ?
          formatDateWithTimezone(new Date(stream.schedule_time), stream.schedule_timezone) :
          (stream.schedule_time ? formatDate(new Date(stream.schedule_time)) : '');
        const scheduleTime = stream.schedule_time && stream.schedule_timezone ?
          formatTimeWithTimezone(new Date(stream.schedule_time), stream.schedule_timezone) :
          (stream.schedule_time ? formatTime(new Date(stream.schedule_time)) : '');

        let settingsDisplay = '';
        if (stream.use_advanced_settings) {
          settingsDisplay = `${stream.resolution || '1280x720'} • ${stream.bitrate || '2500'} kbps • ${stream.fps || '30'} FPS`;
        } else {
          const videoRes = stream.video_resolution || stream.resolution || '1280x720';
          const videoBitrate = stream.video_bitrate || stream.bitrate || '2500';
          const videoFps = stream.video_fps || stream.fps || '30';
          settingsDisplay = `${videoRes} • ${videoBitrate} kbps • ${videoFps} FPS`;
        }

        let formattedDuration = '—';
        if (stream.duration) {
          if (stream.duration >= 60) {
            const hours = Math.floor(stream.duration / 60);
            const minutes = stream.duration % 60;
            formattedDuration = `${hours}h${minutes > 0 ? ` ${minutes}m` : ''}`;
          } else {
            formattedDuration = `${stream.duration} min`;
          }
        }

        let durationDisplay = '';
        if (stream.status === 'live' && stream.start_time) {
          const startTime = new Date(stream.start_time);
          const now = new Date();
          const durationMs = now - startTime;
          const hours = Math.floor(durationMs / (1000 * 60 * 60)).toString().padStart(2, '0');
          const minutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60)).toString().padStart(2, '0');
          const seconds = Math.floor((durationMs % (1000 * 60)) / 1000).toString().padStart(2, '0');
          durationDisplay = `${hours}:${minutes}:${seconds}`;
        }

        card.innerHTML = `
          <div class="relative">
            <div class="aspect-video bg-dark-700 relative overflow-hidden">
              <img src="${thumbnail}" class="w-full h-full object-cover" alt="${stream.title}">
              <div class="absolute top-2 md:top-3 right-2 md:right-3">
                ${statusBadge}
              </div>
              ${stream.status === 'live' ? `
              <div class="absolute bottom-2 md:bottom-3 right-2 md:right-3 bg-black/80 rounded-md px-2 py-1">
                <span class="text-xs font-medium text-white">
                  <span class="live-duration" data-start-time="${stream.start_time}">${durationDisplay}</span>
                </span>
              </div>` : ''}
              <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
            </div>
          </div>

          <div class="p-4 md:p-5">
            <div class="flex items-start justify-between mb-2 md:mb-3">
              <h3 class="font-semibold text-base md:text-lg text-white leading-tight flex-1 mr-2 md:mr-3">${stream.title}</h3>
              <div class="flex items-center text-xs md:text-sm text-gray-400 flex-shrink-0">
                <i class="ti ti-${getPlatformIcon(stream.platform)} text-${getPlatformColor(stream.platform)} mr-1 md:mr-1.5"></i>
                <span class="hidden sm:inline">${stream.platform || 'Custom'}</span>
              </div>
            </div>

            <div class="text-xs text-gray-400 mb-2 md:mb-3 font-medium">
              ${settingsDisplay}
            </div>

            <div class="space-y-1 md:space-y-2 mb-3 md:mb-4">
              <div class="flex items-center text-xs md:text-sm text-gray-400">
                <i class="ti ti-clock mr-1.5 md:mr-2 text-gray-500"></i>
                <span>Duration: ${formattedDuration}</span>
              </div>

              ${stream.status === 'scheduled' ? `
              <div class="flex items-center text-xs md:text-sm text-yellow-500">
                <i class="ti ti-calendar-event mr-1.5 md:mr-2"></i>
                <span class="truncate">Starts ${scheduleDate} • ${scheduleTime} UTC</span>
              </div>` : `
              <div class="flex items-center text-xs md:text-sm text-gray-400">
                <i class="ti ti-calendar mr-1.5 md:mr-2 text-gray-500"></i>
                <span>${startDate || 'Not started'}</span>
              </div>`}
            </div>

            <div class="flex items-center justify-between pt-2 md:pt-3 border-t border-gray-700">
              <div class="flex items-center gap-1 md:gap-2">
                ${getActionButtonHTML(actualStatus, stream.id, 'responsive')}
              </div>
              <div class="flex items-center gap-1 md:gap-2">
                ${isStreamRunning ? `
                  <button disabled
                    class="p-1.5 md:p-2 text-gray-600 cursor-not-allowed rounded-lg"
                    title="Stream sedang berjalan - tidak dapat diedit">
                    <i class="ti ti-edit text-sm"></i>
                  </button>
                  <button disabled
                    class="p-1.5 md:p-2 text-gray-600 cursor-not-allowed rounded-lg"
                    title="Stream sedang berjalan - tidak dapat dihapus">
                    <i class="ti ti-trash text-sm"></i>
                  </button>
                ` : `
                  <button onclick="editStream('${stream.id}')"
                    class="p-1.5 md:p-2 text-gray-400 hover:text-primary hover:bg-primary/10 rounded-lg transition-colors"
                    title="Edit Stream">
                    <i class="ti ti-edit text-sm"></i>
                  </button>
                  <button onclick="deleteStream('${stream.id}')"
                    class="p-1.5 md:p-2 text-gray-400 hover:text-red-400 hover:bg-red-400/10 rounded-lg transition-colors"
                    title="Delete Stream">
                    <i class="ti ti-trash text-sm"></i>
                  </button>
                `}
              </div>
            </div>
          </div>
        `;

        return card;
      }

      function createStreamTableRow(stream) {
        const row = document.createElement('tr');
        row.className = 'hover:bg-dark-700/50 transition-colors';
        row.dataset.streamId = stream.id;
        const thumbnail = stream.video_thumbnail ?
          stream.video_thumbnail :
          'https://via.placeholder.com/320x180?text=No+Preview';
        const startDate = stream.start_time ? new Date(stream.start_time) : null;
        // Use timezone-aware formatting for scheduled streams
        const scheduleDate = stream.schedule_time && stream.schedule_timezone ?
          formatDateWithTimezone(new Date(stream.schedule_time), stream.schedule_timezone) :
          (stream.schedule_time ? formatDate(new Date(stream.schedule_time)) : null);
        const scheduleTime = stream.schedule_time && stream.schedule_timezone ?
          formatTimeWithTimezone(new Date(stream.schedule_time), stream.schedule_timezone) :
          (stream.schedule_time ? formatTime(new Date(stream.schedule_time)) : null);
        let settingsDisplay = '';
        if (stream.use_advanced_settings) {
          settingsDisplay = `${stream.resolution || '1280x720'} • ${stream.bitrate || '2500'} kbps • ${stream.fps || '30'} FPS`;
        } else {
          const videoRes = stream.video_resolution || stream.resolution || '1280x720';
          const videoBitrate = stream.video_bitrate || stream.bitrate || '2500';
          const videoFps = stream.video_fps || stream.fps || '30';
          settingsDisplay = `${videoRes} • ${videoBitrate} kbps • ${videoFps} FPS`;
        }
        let durationDisplay = '';
        let statusBadge = '';
        if (stream.status === 'live' && stream.start_time) {
          const startTime = new Date(stream.start_time);
          const now = new Date();
          const durationMs = now - startTime;
          const hours = Math.floor(durationMs / (1000 * 60 * 60)).toString().padStart(2, '0');
          const minutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60)).toString().padStart(2, '0');
          const seconds = Math.floor((durationMs % (1000 * 60)) / 1000).toString().padStart(2, '0');
          durationDisplay = `${hours}:${minutes}:${seconds}`;
          statusBadge = `
            <span class="flex items-center bg-red-400/10 text-red-400 rounded-full px-2.5 py-1">
              <span class="w-1.5 h-1.5 rounded-full bg-red-400 animate-pulse mr-1.5"></span>
              <span class="text-xs font-medium">Live • <span class="live-duration" data-start-time="${stream.start_time}">
                ${durationDisplay}</span></span>
            </span>
          `;
        } else {
          // Use enhanced status validation with running detection
          let actualStatus = stream.status;
          let isStreamRunning = false;

          if (stream.status === 'live' && !stream.isReallyActive) {
            actualStatus = 'inconsistent'; // Show as inconsistent if DB says live but process is not active
          } else if (stream.isReallyActive) {
            actualStatus = 'live'; // Show as live if process is actually active
            isStreamRunning = true;
          }

          // Additional check: if stream is in memory (active) regardless of DB status
          if (stream.isReallyActive) {
            isStreamRunning = true;
          }

          statusBadge = getStatusBadgeHTML(actualStatus);
        }
        let formattedDuration = '—';
        if (stream.duration) {
          if (stream.duration >= 60) {
            const hours = Math.floor(stream.duration / 60);
            const minutes = stream.duration % 60;
            formattedDuration = `${hours}h${minutes > 0 ? ` ${minutes}m` : ''}`;
          } else {
            formattedDuration = `${stream.duration} min`;
          }
        }
        row.innerHTML = `
        <td class="px-6 py-4 whitespace-nowrap">
          <div class="flex items-center">
            <div class="w-20 h-12 bg-dark-700 rounded flex-shrink-0 overflow-hidden mr-3">
              <img src="${thumbnail}" class="w-full h-full object-cover" alt="${stream.title}">
            </div>
            <div>
              <div class="text-sm font-medium">${stream.title}</div>
              <div class="text-xs text-gray-400">${settingsDisplay}</div>
            </div>
          </div>
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
          <div class="flex items-center">
            <i class="ti ti-${getPlatformIcon(stream.platform)} text-${getPlatformColor(stream.platform)} mr-1.5"></i>
            <span class="text-sm">${stream.platform || 'Custom'}</span>
          </div>
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
          <div class="text-sm text-gray-400">${formatDate(startDate) || '--'}</div>
          ${startDate ? `<div class="text-xs text-gray-500">Started: ${formatTime(startDate)}</div>` : ''}
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
          ${scheduleDate
            ? `<div class="text-sm text-yellow-500 font-medium">${scheduleDate}</div>
               <div class="text-xs text-gray-400">${scheduleTime}</div>`
            : `<div class="text-sm">--</div>`}
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
          <div class="flex items-center">
            <i class="ti ti-clock text-gray-400 mr-1.5"></i>
            <span class="text-sm">${formattedDuration}</span>
          </div>
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
          <div class="flex items-center">
            ${statusBadge}
          </div>
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-right">
          <div class="flex items-center justify-end space-x-2">
            ${getActionButtonHTML(actualStatus, stream.id, 'desktop')}
            <div class="ml-2 flex items-center gap-1">
              ${isStreamRunning ? `
                <button disabled class="p-1.5 rounded cursor-not-allowed" title="Stream sedang berjalan - tidak dapat diedit">
                  <i class="ti ti-edit text-gray-600"></i>
                </button>
                <button disabled class="p-1.5 rounded cursor-not-allowed" title="Stream sedang berjalan - tidak dapat dihapus">
                  <i class="ti ti-trash text-gray-600"></i>
                </button>
              ` : `
                <button class="p-1.5 hover:bg-dark-700 rounded transition-colors" onclick="editStream('${stream.id}')" title="Edit Stream">
                  <i class="ti ti-edit text-gray-400 hover:text-white"></i>
                </button>
                <button class="p-1.5 hover:bg-dark-700 rounded transition-colors" onclick="deleteStream('${stream.id}')" title="Delete Stream">
                  <i class="ti ti-trash text-gray-400 hover:text-red-400"></i>
                </button>
              `}
            </div>
          </div>
        </td>
      `;
        return row;
      }
      function showEmptyState() {
        // Hide streams grid and show empty state
        const streamsGrid = document.getElementById('streams-grid');
        const emptyState = document.getElementById('streams-empty-state');
        if (streamsGrid) {
          streamsGrid.style.display = 'none';
        }
        if (emptyState) {
          emptyState.style.display = 'block';
        }
      }
      function formatDate(date) {
        if (!date) return '';
        return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
      }
      function formatTime(date) {
        if (!date) return '';
        const hours = String(date.getUTCHours()).padStart(2, '0');
        const minutes = String(date.getUTCMinutes()).padStart(2, '0');
        return `${hours}:${minutes}`;
      }

      // Timezone-aware formatting functions
      function formatDateWithTimezone(date, timezone) {
        if (!date || !timezone) return '';
        try {
          return date.toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric',
            timeZone: timezone
          });
        } catch (error) {
          console.error('Error formatting date with timezone:', error);
          return formatDate(date);
        }
      }

      function formatTimeWithTimezone(date, timezone) {
        if (!date || !timezone) return '';
        try {
          // Pastikan date adalah UTC time dari database
          const utcDate = new Date(date);
          
          // Konversi ke timezone yang diinginkan
          return utcDate.toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: false,
            timeZone: timezone
          });
        } catch (error) {
          console.error('Error formatting time with timezone:', error);
          return formatTime(date);
        }
      }
      function getPlatformIcon(platform) {
        switch (platform) {
          case 'YouTube': return 'brand-youtube';
          case 'Facebook': return 'brand-facebook';
          case 'Twitch': return 'brand-twitch';
          case 'TikTok': return 'brand-tiktok';
          case 'Instagram': return 'brand-instagram';
          case 'Shopee Live': return 'shopping-bag';
          case 'Restream.io': return 'live-photo';
          default: return 'broadcast';
        }
      }
      function getPlatformColor(platform) {
        switch (platform) {
          case 'YouTube': return 'red-500';
          case 'Facebook': return 'blue-500';
          case 'Twitch': return 'purple-500';
          case 'TikTok': return 'gray-100';
          case 'Instagram': return 'pink-500';
          case 'Shopee Live': return 'orange-500';
          case 'Restream.io': return 'teal-500';
          default: return 'gray-400';
        }
      }
      function getStatusBadgeHTML(status, duration = '') {
        switch (status) {
          case 'live':
            return `
            <span class="flex items-center bg-red-400/10 text-red-400 rounded-full px-2.5 py-1">
              <span class="w-1.5 h-1.5 rounded-full bg-red-400 animate-pulse mr-1.5"></span>
              <span class="text-xs font-medium">Live${duration ? ' • ' + duration : ''}</span>
            </span>
          `;
          case 'starting':
            return `
            <span class="flex items-center bg-blue-400/10 text-blue-400 rounded-full px-2.5 py-1">
              <i class="ti ti-loader animate-spin text-xs mr-1.5"></i>
              <span class="text-xs font-medium">Starting...</span>
            </span>
          `;
          case 'scheduled':
            return `
            <span class="flex items-center bg-yellow-500/10 text-yellow-500 rounded-full px-2.5 py-1">
              <i class="ti ti-calendar-event text-xs mr-1.5"></i>
              <span class="text-xs font-medium">Scheduled</span>
            </span>
          `;
          case 'error':
            return `
            <span class="flex items-center bg-red-500/10 text-red-500 rounded-full px-2.5 py-1">
              <i class="ti ti-alert-circle text-xs mr-1.5"></i>
              <span class="text-xs font-medium">Error</span>
            </span>
          `;
          case 'inconsistent':
            return `
            <span class="flex items-center bg-orange-500/10 text-orange-500 rounded-full px-2.5 py-1">
              <i class="ti ti-alert-triangle text-xs mr-1.5"></i>
              <span class="text-xs font-medium">Status Issue</span>
            </span>
          `;
          case 'offline':
          default:
            return `
            <span class="flex items-center bg-gray-700 text-gray-400 rounded-full px-2.5 py-1">
              <i class="ti ti-circle-dot text-xs mr-1.5"></i>
              <span class="text-xs font-medium">Offline</span>
            </span>
          `;
        }
      }
      function getActionButtonHTML(status, streamId, view = 'desktop') {
        const btnClass = view === 'mobile'
          ? 'px-4 py-1.5 text-white text-xs font-medium rounded transition-colors'
          : view === 'responsive'
          ? 'inline-flex items-center px-2 md:px-2.5 py-1 md:py-1.5 text-white text-xs font-medium rounded transition-colors'
          : 'inline-flex items-center px-2.5 py-1.5 text-white text-xs font-medium rounded transition-colors';
        switch (status) {
          case 'live':
            return `
            <button
              onclick="stopStream('${streamId}')"
              class="${btnClass} bg-red-500 hover:bg-red-600">
              Stop
            </button>
          `;
          case 'starting':
            return `
            <button
              disabled
              class="${btnClass} bg-blue-400 opacity-50 cursor-not-allowed">
              <i class="ti ti-loader animate-spin mr-1"></i>
              Starting...
            </button>
          `;
          case 'scheduled':
            return `
            <button
              onclick="cancelSchedule('${streamId}')"
              class="${btnClass} bg-yellow-500 hover:bg-yellow-600">
              Cancel
            </button>
          `;
          case 'error':
            return `
            <button
              onclick="clearFailedStream('${streamId}')"
              class="${btnClass} bg-orange-500 hover:bg-orange-600"
              title="Clear error status and reset to offline">
              Clear Error
            </button>
          `;
          case 'inconsistent':
            return `
            <button
              onclick="fixStreamStatus('${streamId}')"
              class="${btnClass} bg-yellow-500 hover:bg-yellow-600"
              title="Fix status inconsistency">
              Fix Status
            </button>
          `;
          case 'offline':
          default:
            return `
            <button
              onclick="startStream('${streamId}')"
              class="${btnClass} bg-primary hover:bg-secondary">
              Start
            </button>
          `;
        }
      }

    function startStream(streamId) {
      if (!streamId) return;
      const actionBtns = document.querySelectorAll(`[data-stream-id="${streamId}"] .action-btn`);
      const originalBtnStates = [];
      actionBtns.forEach(btn => {
        originalBtnStates.push({
          element: btn,
          html: btn.innerHTML,
          disabled: btn.disabled
        });
        btn.innerHTML = '<i class="ti ti-loader animate-spin"></i> Starting...';
        btn.disabled = true;
      });
      function restoreButtons() {
        originalBtnStates.forEach(state => {
          state.element.innerHTML = state.html;
          state.element.disabled = state.disabled;
        });
      }
      fetch(`/api/streams/${streamId}/status`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: 'live' })
      })
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            const streamMode = data.isAdvancedMode ? "Advanced mode" : "Simple mode";
            if (typeof notifications !== 'undefined') {
              notifications.success('Stream Started', `Stream started successfully! (${streamMode})`);
            } else {
              alert(`Stream started successfully! (${streamMode})`);
            }
            window.location.reload();
          } else {
            // Improved error message extraction for stream start
            let errorMessage = 'Failed to start stream';

            if (typeof data.error === 'string') {
              errorMessage = data.error;
            } else if (data.error && typeof data.error === 'object') {
              if (data.error.message) {
                errorMessage = data.error.message;
              } else if (data.error.error) {
                errorMessage = data.error.error;
              } else if (data.error.userMessage) {
                errorMessage = data.error.userMessage;
              } else {
                // Use extractErrorMessage utility if available
                if (typeof window.extractErrorMessage === 'function') {
                  errorMessage = window.extractErrorMessage(data.error, 'Failed to start stream');
                }
              }
            }

            if (typeof notifications !== 'undefined') {
              notifications.error('Start Failed', errorMessage);
            } else {
              alert(`Error: ${errorMessage}`);
            }
            restoreButtons();
          }
        })
        .catch(error => {
          // console.error('Error starting stream:', error); // Cleaned for production
          let errorMessage = 'An error occurred while starting the stream';

          // Extract meaningful error message
          if (error.message) {
            errorMessage = error.message;
          } else if (typeof error === 'string') {
            errorMessage = error;
          }

          // Handle HTTP error responses
          if (error.message && error.message.includes('HTTP')) {
            if (error.message.includes('403')) {
              errorMessage = 'Access denied. You may need to upgrade your plan or check your subscription status.';
            } else if (error.message.includes('401')) {
              errorMessage = 'Authentication failed. Please refresh the page and try again.';
            }
          }

          if (typeof notifications !== 'undefined') {
            notifications.error('Start Failed', errorMessage);
          } else {
            alert(errorMessage);
          }
          restoreButtons();
        });
    }
    function stopStream(streamId) {
      if (!streamId) return;
      const actionBtns = document.querySelectorAll(`[data-stream-id="${streamId}"] .action-btn`);
      const originalBtnStates = [];
      actionBtns.forEach(btn => {
        originalBtnStates.push({
          element: btn,
          html: btn.innerHTML,
          disabled: btn.disabled
        });
        btn.innerHTML = '<i class="ti ti-loader animate-spin"></i> Stopping...';
        btn.disabled = true;
      });
      function restoreButtons() {
        originalBtnStates.forEach(state => {
          state.element.innerHTML = state.html;
          state.element.disabled = state.disabled;
        });
      }
      fetch(`/api/streams/${streamId}/status`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: 'offline' })
      })
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            if (typeof notifications !== 'undefined') {
              notifications.success('Stream Stopped', 'Stream stopped successfully!');
            } else {
              alert('Stream stopped successfully!');
            }
            window.location.reload();
          } else {
            // Improved error message extraction for stream stop
            let errorMessage = 'Failed to stop stream';

            if (typeof data.error === 'string') {
              errorMessage = data.error;
            } else if (data.error && typeof data.error === 'object') {
              if (data.error.message) {
                errorMessage = data.error.message;
              } else if (data.error.error) {
                errorMessage = data.error.error;
              } else if (data.error.userMessage) {
                errorMessage = data.error.userMessage;
              } else {
                // Use extractErrorMessage utility if available
                if (typeof window.extractErrorMessage === 'function') {
                  errorMessage = window.extractErrorMessage(data.error, 'Failed to stop stream');
                }
              }
            }

            if (typeof notifications !== 'undefined') {
              notifications.error('Stop Failed', errorMessage);
            } else {
              alert(`Error: ${errorMessage}`);
            }
            restoreButtons();
          }
        })
        .catch(error => {
          // console.error('Error stopping stream:', error); // Cleaned for production
          let errorMessage = 'An error occurred while stopping the stream';

          // Extract meaningful error message
          if (error.message) {
            errorMessage = error.message;
          } else if (typeof error === 'string') {
            errorMessage = error;
          }

          if (typeof notifications !== 'undefined') {
            notifications.error('Stop Failed', errorMessage);
          } else {
            alert(errorMessage);
          }
          restoreButtons();
        });
    }
    function cancelSchedule(streamId) {
      if (!streamId) return;
      const actionBtns = document.querySelectorAll(`[data-stream-id="${streamId}"] .action-btn`);
      const originalBtnStates = [];
      actionBtns.forEach(btn => {
        originalBtnStates.push({
          element: btn,
          html: btn.innerHTML,
          disabled: btn.disabled
        });
        btn.innerHTML = '<i class="ti ti-loader animate-spin"></i> Cancelling...';
        btn.disabled = true;
      });
      function restoreButtons() {
        originalBtnStates.forEach(state => {
          state.element.innerHTML = state.html;
          state.element.disabled = state.disabled;
        });
      }
      fetch(`/api/streams/${streamId}/status`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: 'offline' })
      })
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            if (typeof notifications !== 'undefined') {
              notifications.success('Schedule Cancelled', 'Schedule cancelled successfully!');
            } else {
              alert('Schedule cancelled successfully!');
            }
            window.location.reload();
          } else {
            // Improved error message extraction for schedule cancellation
            let errorMessage = 'Failed to cancel schedule';

            if (typeof data.error === 'string') {
              errorMessage = data.error;
            } else if (data.error && typeof data.error === 'object') {
              if (data.error.message) {
                errorMessage = data.error.message;
              } else if (data.error.error) {
                errorMessage = data.error.error;
              } else if (data.error.userMessage) {
                errorMessage = data.error.userMessage;
              } else {
                // Use extractErrorMessage utility if available
                if (typeof window.extractErrorMessage === 'function') {
                  errorMessage = window.extractErrorMessage(data.error, 'Failed to cancel schedule');
                }
              }
            }

            if (typeof notifications !== 'undefined') {
              notifications.error('Cancel Failed', errorMessage);
            } else {
              alert(`Error: ${errorMessage}`);
            }
            restoreButtons();
          }
        })
        .catch(error => {
          // console.error('Error cancelling schedule:', error); // Cleaned for production
          let errorMessage = 'An error occurred while cancelling the schedule';

          // Extract meaningful error message
          if (error.message) {
            errorMessage = error.message;
          } else if (typeof error === 'string') {
            errorMessage = error;
          }

          if (typeof notifications !== 'undefined') {
            notifications.error('Cancel Failed', errorMessage);
          } else {
            alert(errorMessage);
          }
          restoreButtons();
        });
    }

    function fixStreamStatus(streamId) {
      if (!streamId) return;

      if (!confirm('This will force synchronize the stream status. Continue?')) {
        return;
      }

      fetch('/api/admin/sync-stream-status', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      })
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            if (typeof notifications !== 'undefined') {
              notifications.success('Status Synced', 'Stream status synchronized successfully!');
            } else {
              alert('Stream status synchronized successfully!');
            }
            window.location.reload();
          } else {
            // Improved error message extraction for sync status
            let errorMessage = 'Failed to sync stream status';

            if (typeof data.error === 'string') {
              errorMessage = data.error;
            } else if (data.error && typeof data.error === 'object') {
              if (data.error.message) {
                errorMessage = data.error.message;
              } else if (data.error.error) {
                errorMessage = data.error.error;
              } else if (data.error.userMessage) {
                errorMessage = data.error.userMessage;
              } else {
                // Use extractErrorMessage utility if available
                if (typeof window.extractErrorMessage === 'function') {
                  errorMessage = window.extractErrorMessage(data.error, 'Failed to sync stream status');
                }
              }
            }

            if (typeof notifications !== 'undefined') {
              notifications.error('Sync Failed', errorMessage);
            } else {
              alert(`Error: ${errorMessage}`);
            }
          }
        })
        .catch(error => {
          let errorMessage = 'An error occurred while syncing stream status';

          // Extract meaningful error message
          if (error.message) {
            errorMessage = error.message;
          } else if (typeof error === 'string') {
            errorMessage = error;
          }

          if (typeof notifications !== 'undefined') {
            notifications.error('Sync Failed', errorMessage);
          } else {
            alert(errorMessage);
          }
        });
    }

    async function deleteStream(streamId) {
      if (!streamId) return;

      const confirmed = await notifications.confirm(
        'Are you sure you want to delete this stream?',
        'Delete Stream',
        {
          confirmText: 'Delete',
          type: 'warning'
        }
      );

      if (confirmed) {
        const loading = notifications.loading('Deleting stream...', 'Please wait while we delete the stream');

        try {
          const response = await fetch(`/api/streams/${streamId}`, {
            method: 'DELETE',
            headers: {
              'Content-Type': 'application/json'
            }
          });

          const data = await response.json();
          loading.close();

          if (data.success) {
            notifications.success('Stream Deleted', 'Stream has been deleted successfully');
            setTimeout(() => window.location.reload(), 1000);
          } else {
            if (data.code === 'STREAM_LIVE_LOCKED') {
              // 🔒 Handle stream live protection
              notifications.warning('Stream Sedang Live', data.error || 'Stream tidak dapat dihapus saat sedang live');
            } else {
              notifications.error('Delete Failed', data.error || 'Failed to delete stream');
            }
          }
        } catch (error) {
          loading.close();
          console.error('Error deleting stream:', error);
          notifications.error('Network Error', 'An error occurred while deleting the stream');
        }
      }
    }
    function startCountdowns() {
      const updateTimers = () => {
        document.querySelectorAll('[data-schedule-time]').forEach(el => {
          const scheduleTime = new Date(el.dataset.scheduleTime);
          const now = new Date();
          const diff = scheduleTime - now;
          if (diff <= 0) {
            el.textContent = 'Starting soon...';
            return;
          }
          const days = Math.floor(diff / (1000 * 60 * 60 * 24));
          const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
          const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
          const seconds = Math.floor((diff % (1000 * 60)) / 1000);
          let timeText = '';
          if (days > 0) timeText = `${days}d ${hours}h ${minutes}m`;
          else if (hours > 0) timeText = `${hours}h ${minutes}m ${seconds}s`;
          else timeText = `${minutes}m ${seconds}s`;
          el.textContent = `Starts in: ${timeText}`;
        });
      };
      updateTimers();
      setInterval(updateTimers, 1000);
    }
    document.addEventListener('DOMContentLoaded', function () {
      startCountdowns();
    });
    let editSelectedVideoData = null;
    let currentEditOrientation = 'horizontal';
    let editDesktopVideoPlayer = null;
    let editMobileVideoPlayer = null;
    let isEditStreamKeyValid = true;
    let originalStreamKey = '';

    // Test functions removed to prevent conflicts with stream-modal.js

    // Modal functions are now handled by stream-modal.js
    // Removed duplicate function definitions to prevent conflicts

    // Debug functions removed to prevent conflicts with stream-modal.js

    // Modal functions are now handled by stream-modal.js
    // console.log('Modal functions will be loaded from stream-modal.js'); // Removed for production
    async function editStream(streamId) {
      // console.log('editStream called with ID:', streamId); // Removed for production
      if (!streamId) {
        console.error('No streamId provided');
        return;
      }

      // Check if notifications system is available
      let loading;
      try {
        if (typeof notifications !== 'undefined') {
          loading = notifications.loading('Loading stream data...', 'Please wait while we fetch the stream information');
        } else {
          // console.log('Notifications system not available, using fallback'); // Removed for production
        }
      } catch (e) {
        console.warn('Error with notifications system:', e);
      }

      try {
        // console.log('Fetching stream data for ID:', streamId); // Removed for production
        const response = await fetch(`/api/streams/${streamId}`);
        const data = await response.json();
        // console.log('Stream data received:', data); // Removed for production
        if (loading) loading.close();

        if (data.success) {
          // console.log('Opening edit modal with stream data'); // Removed for production
          // First populate the form data
          if (populateEditStreamForm(data.stream)) {
            // Then open the modal using the function from stream-modal.js
            window.openEditStreamModal(data.stream);
          } else {
            console.error('Failed to populate edit form');
            if (typeof notifications !== 'undefined') {
              notifications.error('Form Error', 'Failed to populate edit form');
            } else {
              alert('Failed to populate edit form');
            }
          }
        } else {
          console.error('Failed to fetch stream data:', data.error);
          if (typeof notifications !== 'undefined') {
            notifications.error('Load Failed', data.error || 'Failed to fetch stream data');
          } else {
            alert('Error: ' + (data.error || 'Failed to fetch stream data'));
          }
        }
      } catch (error) {
        if (loading) loading.close();
        console.error('Error fetching stream data:', error);
        if (typeof notifications !== 'undefined') {
          notifications.error('Network Error', 'An error occurred while fetching stream data');
        } else {
          alert('An error occurred while fetching stream data');
        }
      }
    }
    // openEditStreamModal function moved to stream-modal.js to prevent conflicts
    // This function now handles the data population logic only
    function populateEditStreamForm(stream) {
      // console.log('populateEditStreamForm called with stream:', stream); // Removed for production
      try {
        originalStreamKey = stream.stream_key;

        // Check if required elements exist
        const editStreamId = document.getElementById('editStreamId');
        const editStreamTitle = document.getElementById('editStreamTitle');
        const editRtmpUrl = document.getElementById('editRtmpUrl');
        const editStreamKey = document.getElementById('editStreamKey');

        if (!editStreamId || !editStreamTitle || !editRtmpUrl || !editStreamKey) {
          console.error('Missing required form elements for edit modal');
          // console.log('editStreamId:', !!editStreamId); // Removed for production
          // console.log('editStreamTitle:', !!editStreamTitle); // Removed for production
          // console.log('editRtmpUrl:', !!editRtmpUrl); // Removed for production
          // console.log('editStreamKey:', !!editStreamKey); // Removed for production
          return false;
        }

        editStreamId.value = stream.id;
        editStreamTitle.value = stream.title;
        editRtmpUrl.value = stream.rtmp_url || '';
        editStreamKey.value = stream.stream_key || '';

        if (stream.video_id) {
          document.getElementById('editSelectedVideoId').value = stream.video_id;
          document.getElementById('editSelectedVideo').textContent = stream.video_title || 'Selected Video';
          if (stream.video_filepath) {
            const videoData = {
              id: stream.video_id,
              name: stream.video_title,
              url: `/stream/${stream.video_id}`,
              thumbnail: stream.video_thumbnail
            };
            selectEditVideo(videoData);
          }
        }

        document.getElementById('editLoopVideo').checked = stream.loop_video;

        const bitrateSelect = document.getElementById('editBitrate');
        for (let i = 0; i < bitrateSelect.options.length; i++) {
          if (bitrateSelect.options[i].value == stream.bitrate) {
            bitrateSelect.selectedIndex = i;
            break;
          }
        }

        currentEditOrientation = stream.orientation || 'horizontal';
        setEditVideoOrientation(currentEditOrientation);

        const resolutionSelect = document.getElementById('editResolutionSelect');
        if (stream.resolution) {
          let found = false;
          for (let i = 0; i < resolutionSelect.options.length; i++) {
            const option = resolutionSelect.options[i];
            const resValue = option.getAttribute(`data-${currentEditOrientation}`);
            if (resValue === stream.resolution) {
              resolutionSelect.selectedIndex = i;
              found = true;
              break;
            }
          }
          if (!found) {
            resolutionSelect.value = "720";
          }
          // Only call updateEditResolutionDisplay if elements are ready
          setTimeout(() => {
            updateEditResolutionDisplay();
          }, 10);
        }

        const fpsSelect = document.getElementById('editFps');
        for (let i = 0; i < fpsSelect.options.length; i++) {
          if (fpsSelect.options[i].value == stream.fps) {
            fpsSelect.selectedIndex = i;
            break;
          }
        }

        if (stream.schedule_time) {
          const scheduleDate = new Date(stream.schedule_time);
          const formattedDate = scheduleDate.toISOString().slice(0, 16);
          document.getElementById('editScheduleTime').value = formattedDate;

          // Set timezone with proper display
          const timezoneHidden = document.getElementById('editScheduleTimezone');
          const timezoneSearch = document.getElementById('editScheduleTimezoneSearch');
          if (timezoneHidden && timezoneSearch && stream.schedule_timezone) {
            timezoneHidden.value = stream.schedule_timezone;
            // Find and display the timezone label
            const timezoneObj = availableTimezones.find(tz => tz.value === stream.schedule_timezone);
            if (timezoneObj) {
              timezoneSearch.value = timezoneObj.label;
            } else {
              timezoneSearch.value = stream.schedule_timezone; // Fallback
            }
          }
        } else {
          document.getElementById('editScheduleTime').value = '';
          const timezoneHidden = document.getElementById('editScheduleTimezone');
          const timezoneSearch = document.getElementById('editScheduleTimezoneSearch');
          if (timezoneHidden && timezoneSearch) {
            timezoneHidden.value = defaultTimezone;
            // Find and display the default timezone label
            const timezoneObj = availableTimezones.find(tz => tz.value === defaultTimezone);
            if (timezoneObj) {
              timezoneSearch.value = timezoneObj.label;
            } else {
              timezoneSearch.value = defaultTimezone; // Fallback
            }
          }
        }

        if (stream.duration) {
          document.getElementById('editDuration').value = stream.duration;
        } else {
          document.getElementById('editDuration').value = '';
        }

        const advancedSettingsContent = document.getElementById('editAdvancedSettingsContent');
        const advancedSettingsToggle = document.getElementById('editAdvancedSettingsToggle');
        const icon = advancedSettingsToggle.querySelector('i');
        if (stream.use_advanced_settings) {
          advancedSettingsContent.classList.remove('hidden');
          icon.style.transform = 'rotate(180deg)';
        } else {
          advancedSettingsContent.classList.add('hidden');
          icon.style.transform = '';
        }

        // Check copy mode compatibility for the selected video
        if (stream.video_id) {
          checkEditCopyModeCompatibility(stream.video_id);
        }

        return true;
      } catch (error) {
        console.error('Error in populateEditStreamForm:', error);
        return false;
      }
    }
    // closeEditStreamModal function moved to stream-modal.js to prevent conflicts
    function resetEditModalForm() {
      document.getElementById('editStreamForm').reset();
      document.getElementById('editStreamId').value = '';
      document.getElementById('editSelectedVideoId').value = '';
      document.getElementById('editSelectedVideo').textContent = 'Choose a video...';
      const desktopPreview = document.getElementById('editVideoPreview');
      const desktopEmptyPreview = document.getElementById('editEmptyPreview');
      const mobilePreview = document.getElementById('editVideoPreviewMobile');
      const mobileEmptyPreview = document.getElementById('editEmptyPreviewMobile');
      desktopPreview.classList.add('hidden');
      mobilePreview.classList.add('hidden');
      desktopEmptyPreview.classList.remove('hidden');
      mobileEmptyPreview.classList.remove('hidden');
      const advancedSettingsContent = document.getElementById('editAdvancedSettingsContent');
      const advancedSettingsToggle = document.getElementById('editAdvancedSettingsToggle');
      if (advancedSettingsContent && advancedSettingsToggle) {
        advancedSettingsContent.classList.add('hidden');
        const icon = advancedSettingsToggle.querySelector('i');
        if (icon) icon.style.transform = '';
      }
    }
    function toggleEditVideoSelector() {
      const dropdown = document.getElementById('editVideoSelectorDropdown');
      if (dropdown.classList.contains('hidden')) {
        dropdown.classList.remove('hidden');
        if (!dropdown.dataset.loaded) {
          loadEditGalleryVideos();
          dropdown.dataset.loaded = 'true';
        }
      } else {
        dropdown.classList.add('hidden');
      }
    }
    async function loadEditGalleryVideos() {
      const container = document.getElementById('editVideoListContainer');
      if (!container) {
        // console.error("Edit video list container not found"); // Cleaned for production
        return;
      }
      container.innerHTML = '<div class="text-center py-3"><i class="ti ti-loader animate-spin mr-2"></i>Loading videos...</div>';
      try {
        const response = await fetch('/api/stream/videos');
        const videos = await response.json();
        displayEditFilteredVideos(videos);
        const searchInput = document.getElementById('editVideoSearchInput');
        if (searchInput) {
          searchInput.removeEventListener('input', handleEditVideoSearch);
          searchInput.addEventListener('input', handleEditVideoSearch);
          setTimeout(() => searchInput.focus(), 10);
        }
      } catch (error) {
        // console.error('Error loading gallery videos:', error); // Cleaned for production
        container.innerHTML = '<div class="text-center py-5 text-red-400"><i class="ti ti-alert-circle text-2xl mb-2"></i><p>Failed to load videos</p></div>';
      }
    }
    function handleEditVideoSearch(e) {
      const searchTerm = e.target.value.toLowerCase();
      const filteredVideos = window.allStreamVideos.filter(video =>
        video.name.toLowerCase().includes(searchTerm)
      );
      displayEditFilteredVideos(filteredVideos);
    }
    function displayEditFilteredVideos(videos) {
      const container = document.getElementById('editVideoListContainer');
      if (!videos || !videos.length) {
        container.innerHTML = '<div class="text-center py-5 text-gray-400"><p>No videos found</p></div>';
        return;
      }
      container.innerHTML = '';
      videos.forEach(video => {
        const button = document.createElement('button');
        button.type = 'button';
        button.className = 'w-full flex items-center space-x-3 p-2 rounded hover:bg-dark-600 transition-colors';
        button.onclick = () => selectEditVideo(video);
        button.innerHTML = `
      <div class="w-16 h-12 bg-dark-800 rounded flex-shrink-0 overflow-hidden">
        <img src="${video.thumbnail || '/images/default-thumbnail.jpg'}" alt=""
          class="w-full h-full object-cover rounded"
          onerror="this.src='/images/default-thumbnail.jpg'">
      </div>
      <div class="flex-1 min-w-0 ml-3">
        <p class="text-sm font-medium text-white truncate">${video.name}</p>
        <p class="text-xs text-gray-400">${video.resolution || 'Unknown'} • ${video.duration || '00:00'}</p>
      </div>
    `;
        container.appendChild(button);
      });
    }
    function selectEditVideo(video) {
      editSelectedVideoData = video;
      document.getElementById('editSelectedVideo').textContent = video.name;
      document.getElementById('editSelectedVideoId').value = video.id;
      const desktopPreview = document.getElementById('editVideoPreview');
      const desktopEmptyPreview = document.getElementById('editEmptyPreview');
      const mobilePreview = document.getElementById('editVideoPreviewMobile');
      const mobileEmptyPreview = document.getElementById('editEmptyPreviewMobile');
      desktopPreview.classList.remove('hidden');
      mobilePreview.classList.remove('hidden');
      desktopEmptyPreview.classList.add('hidden');
      mobileEmptyPreview.classList.add('hidden');
      createEditVideoPreview(video);
      document.getElementById('editVideoSelectorDropdown').classList.add('hidden');

      // Check copy mode compatibility for the selected video
      if (video.id) {
        checkEditCopyModeCompatibility(video.id);
      }
    }
    function createEditVideoPreview(video) {
      if (editDesktopVideoPlayer) {
        editDesktopVideoPlayer.dispose();
        editDesktopVideoPlayer = null;
      }
      if (editMobileVideoPlayer) {
        editMobileVideoPlayer.dispose();
        editMobileVideoPlayer = null;
      }
      const desktopVideoContainer = document.getElementById('editVideoPreview');
      const mobileVideoContainer = document.getElementById('editVideoPreviewMobile');
      desktopVideoContainer.innerHTML = `
    <video id="edit-videojs-preview-desktop" class="video-js vjs-default-skin vjs-big-play-centered" controls preload="auto">
      <source src="${video.url}" type="video/mp4">
    </video>
  `;
      mobileVideoContainer.innerHTML = `
    <video id="edit-videojs-preview-mobile" class="video-js vjs-default-skin vjs-big-play-centered" controls preload="auto">
      <source src="${video.url}" type="video/mp4">
    </video>
  `;
      setTimeout(() => {
        try {
          editDesktopVideoPlayer = videojs('edit-videojs-preview-desktop', {
            controls: true,
            autoplay: false,
            preload: 'auto',
            fluid: true
          });
          editMobileVideoPlayer = videojs('edit-videojs-preview-mobile', {
            controls: true,
            autoplay: false,
            preload: 'auto',
            fluid: true
          });
        } catch (e) {
          // console.warn('Error initializing video players:', e); // Cleaned for production
        }
      }, 10);
    }
    function setEditVideoOrientation(orientation) {
      currentEditOrientation = orientation;
      const horizontalBtn = document.querySelector('[onclick="setEditVideoOrientation(\'horizontal\')"]');
      const verticalBtn = document.querySelector('[onclick="setEditVideoOrientation(\'vertical\')"]');
      horizontalBtn.classList.remove('edit-active-orientation', 'bg-primary');
      horizontalBtn.classList.add('bg-dark-700');
      verticalBtn.classList.remove('edit-active-orientation', 'bg-primary');
      verticalBtn.classList.add('bg-dark-700');
      if (orientation === 'horizontal') {
        horizontalBtn.classList.add('edit-active-orientation', 'bg-primary');
        horizontalBtn.classList.remove('bg-dark-700');
      } else {
        verticalBtn.classList.add('edit-active-orientation', 'bg-primary');
        verticalBtn.classList.remove('bg-dark-700');
      }
      // Only call updateEditResolutionDisplay if elements are ready
      setTimeout(() => {
        updateEditResolutionDisplay();
      }, 10);
    }

    function toggleEditStreamKeyVisibility() {
      const keyInput = document.getElementById('editStreamKey');
      const eyeIcon = document.getElementById('editStreamKeyToggle');
      if (keyInput.type === 'password') {
        keyInput.type = 'text';
        eyeIcon.classList.remove('ti-eye');
        eyeIcon.classList.add('ti-eye-off');
      } else {
        keyInput.type = 'password';
        eyeIcon.classList.remove('ti-eye-off');
        eyeIcon.classList.add('ti-eye');
      }
    }
    document.addEventListener('DOMContentLoaded', function () {
      const editResolutionSelect = document.getElementById('editResolutionSelect');
      if (editResolutionSelect) {
        editResolutionSelect.addEventListener('change', updateEditResolutionDisplay);
      }
      const editForm = document.getElementById('editStreamForm');
      if (editForm) {
        editForm.addEventListener('submit', async function (e) {
          e.preventDefault();
          const streamId = document.getElementById('editStreamId').value;
          if (!streamId) {
            notifications.error('Missing Data', 'Stream ID is missing.');
            return;
          }
          const videoId = document.getElementById('editSelectedVideoId').value;
          if (!videoId) {
            notifications.warning('No Video Selected', 'Please select a video before updating the stream.');
            return;
          }
          const formData = {
            streamTitle: document.getElementById('editStreamTitle').value,
            videoId: videoId,
            rtmpUrl: document.getElementById('editRtmpUrl').value,
            streamKey: document.getElementById('editStreamKey').value,
            bitrate: document.getElementById('editBitrate').value,
            fps: document.getElementById('editFps').value,
            loopVideo: document.getElementById('editLoopVideo').checked,
            orientation: currentEditOrientation,
            resolution: document.getElementById('editCurrentResolution').textContent,
            useAdvancedSettings: !document.getElementById('editAdvancedSettingsContent').classList.contains('hidden')
          };
          const scheduleTime = document.getElementById('editScheduleTime').value;
          const duration = document.getElementById('editDuration').value;

          // Validate schedule time if provided
          if (scheduleTime) {
            const selectedTimezone = document.getElementById('editDisplayTimezone').value;
            const isValidScheduleTime = validateScheduleTime(scheduleTime, selectedTimezone, 'editScheduleTimeError');
            if (!isValidScheduleTime) {
              return; // Stop form submission if validation fails
            }
            formData.scheduleTime = scheduleTime;
            formData.scheduleTimezone = selectedTimezone; // Kirim ke backend juga
          }
          if (duration) {
            formData.duration = parseInt(duration);
          }
          const csrfToken = document.querySelector('input[name="_csrf"]')?.value;
          const loading = notifications.loading('Updating stream...', 'Please wait while we save your changes');

          try {
            const response = await fetch(`/api/streams/${streamId}`, {
              method: 'PUT',
              headers: {
                'Content-Type': 'application/json',
                ...(csrfToken ? { 'X-CSRF-Token': csrfToken } : {})
              },
              body: JSON.stringify(formData)
            });

            const data = await response.json();
            loading.close();

            if (data.success) {
              let message = 'Stream has been updated successfully!';

              // Handle stream restart scenarios
              if (data.streamRestarted) {
                message = 'Stream has been updated and restarted with new settings!';
                notifications.success('Stream Restarted', message);
              } else if (data.restartError) {
                message = `Stream updated but restart failed: ${data.restartError}`;
                notifications.warning('Partial Update', message);
              } else {
                notifications.success('Stream Updated', message);
              }

              // Show additional info if failed status was cleared
              if (data.failedStatusCleared) {
                setTimeout(() => {
                  notifications.info('Status Cleared', 'Failed stream status has been automatically cleared.');
                }, 1500);
              }

              closeEditStreamModal();
              setTimeout(() => {
                window.location.reload();
              }, 1000);
            } else {
              if (data.requiresUpgrade) {
                notifications.error('Plan Upgrade Required',
                  `${data.error}\n\nHarga plan saat ini: Rp. ${data.currentPlanPrice.toLocaleString('id-ID')}\nMinimal diperlukan: Rp. ${data.minimumPrice.toLocaleString('id-ID')}\n\nSilakan upgrade plan Anda untuk menggunakan advanced settings.`);
              } else if (data.code === 'STREAM_LIVE_LOCKED') {
                // 🔒 Handle stream live protection
                notifications.warning('Stream Sedang Live', data.error || 'Stream tidak dapat diedit saat sedang live');
              } else {
                // Improved error message extraction for stream updates
                let errorMessage = 'Failed to update stream';

                if (typeof data.error === 'string') {
                  errorMessage = data.error;
                } else if (data.error && typeof data.error === 'object') {
                  if (data.error.message) {
                    errorMessage = data.error.message;
                  } else if (data.error.error) {
                    errorMessage = data.error.error;
                  } else if (data.error.userMessage) {
                    errorMessage = data.error.userMessage;
                  }
                }

                notifications.error('Update Failed', errorMessage);
              }
            }
          } catch (error) {
            loading.close();
            console.error('Error:', error);
            notifications.error('Network Error', 'An error occurred while updating the stream');
          }
        });
      }
      startLiveTimers();
      startCountdowns();
    });

    // Copy Mode Compatibility Functions
    let currentVideoCompatibility = null;
    let currentEditVideoCompatibility = null;

    async function initializeCopyModeCompatibility() {
      // Listen for video selection changes
      const videoButtons = document.querySelectorAll('.video-option');
      videoButtons.forEach(button => {
        button.addEventListener('click', async function() {
          const videoId = this.dataset.videoId;
          if (videoId) {
            await checkCopyModeCompatibility(videoId);
          }
        });
      });

      // Listen for advanced settings changes
      const advancedInputs = document.querySelectorAll('#advancedSettingsContent select');
      advancedInputs.forEach(input => {
        input.addEventListener('change', updateCopyModeStatus);
      });

      // Listen for edit advanced settings changes
      const editAdvancedInputs = document.querySelectorAll('#editAdvancedSettingsContent select');
      editAdvancedInputs.forEach(input => {
        input.addEventListener('change', updateEditCopyModeStatus);
      });
    }

    async function checkCopyModeCompatibility(videoId) {
      try {
        const response = await fetch(`/api/videos/${videoId}/copy-mode-settings`);
        const data = await response.json();

        if (data.success) {
          currentVideoCompatibility = data;
          updateAdvancedSettingsForCopyMode(data);
          updateCopyModeStatus();
        }
      } catch (error) {
        console.error('Error checking copy mode compatibility:', error);
      }
    }

    async function checkEditCopyModeCompatibility(videoId) {
      try {
        const response = await fetch(`/api/videos/${videoId}/copy-mode-settings`);
        const data = await response.json();

        if (data.success) {
          currentEditVideoCompatibility = data;
          updateEditAdvancedSettingsForCopyMode(data);
          updateEditCopyModeStatus();
        }
      } catch (error) {
        console.error('Error checking edit copy mode compatibility:', error);
      }
    }

    function updateAdvancedSettingsForCopyMode(compatibilityData) {
      if (!compatibilityData.compatible) {
        // Video not compatible with copy mode
        showCopyModeWarning(compatibilityData.reason);
        return;
      }

      const settings = compatibilityData.settings;

      // Update bitrate options
      const bitrateSelect = document.getElementById('bitrateSelect');
      if (bitrateSelect && settings.bitrateOptions) {
        updateSelectOptions(bitrateSelect, settings.bitrateOptions, 'kbps');
      }

      // Update FPS options
      const fpsSelect = document.getElementById('fpsSelect');
      if (fpsSelect && settings.fpsOptions) {
        updateSelectOptions(fpsSelect, settings.fpsOptions, 'FPS');
      }

      // Update resolution options
      const resolutionSelect = document.getElementById('resolutionSelect');
      if (resolutionSelect && settings.compatibleResolutions) {
        updateResolutionOptions(resolutionSelect, settings.compatibleResolutions);
      }

      hideCopyModeWarning();
    }

    function updateSelectOptions(selectElement, options, suffix) {
      // Store current value
      const currentValue = selectElement.value;

      // Clear existing options
      selectElement.innerHTML = '';

      // Add compatible options
      options.forEach(option => {
        const optionElement = document.createElement('option');
        optionElement.value = option;
        optionElement.textContent = `${option} ${suffix}`;
        selectElement.appendChild(optionElement);
      });

      // Restore value if still compatible, otherwise select first option
      if (options.includes(parseInt(currentValue))) {
        selectElement.value = currentValue;
      } else {
        selectElement.selectedIndex = 0;
      }
    }

    function updateResolutionOptions(selectElement, resolutions) {
      // Store current value
      const currentValue = selectElement.value;

      // Clear existing options
      selectElement.innerHTML = '';

      // Add compatible resolutions
      resolutions.forEach((res, index) => {
        const optionElement = document.createElement('option');
        // Handle both string and object formats
        if (typeof res === 'string') {
          // Simple string format like "1280x720"
          const resolutionValue = res.match(/(\d+)x(\d+)/)?.[0] || res;
          const resolutionLabel = res.includes('x') ? res : `${res}p`;
          const height = resolutionValue.split('x')[1] || res;
          optionElement.value = height; // Use height as value for consistency
          optionElement.setAttribute('data-horizontal', resolutionValue);
          optionElement.setAttribute('data-vertical', resolutionValue.split('x').reverse().join('x'));
          optionElement.textContent = resolutionLabel;
        } else if (res && res.value && res.label) {
          // Object format like { value: "1280x720", label: "720p HD" }
          const [width, height] = res.value.split('x');
          optionElement.value = height; // Use height as value for consistency
          optionElement.setAttribute('data-horizontal', res.value);
          optionElement.setAttribute('data-vertical', `${height}x${width}`);
          optionElement.textContent = res.label;
        }
        selectElement.appendChild(optionElement);
      });

      // Select first option by default
      selectElement.selectedIndex = 0;
      updateResolutionDisplay();
    }

    function updateCopyModeStatus() {
      const copyIndicator = document.getElementById('copyModeIndicator');
      const reencodeIndicator = document.getElementById('reencodeIndicator');

      if (!currentVideoCompatibility) {
        copyIndicator.classList.add('hidden');
        reencodeIndicator.classList.add('hidden');
        return;
      }

      if (currentVideoCompatibility.compatible) {
        // Check if current settings would still use copy mode
        const willUseCopyMode = checkCurrentSettingsCompatibility();

        if (willUseCopyMode) {
          copyIndicator.classList.remove('hidden');
          reencodeIndicator.classList.add('hidden');
        } else {
          copyIndicator.classList.add('hidden');
          reencodeIndicator.classList.remove('hidden');
        }
      } else {
        copyIndicator.classList.add('hidden');
        reencodeIndicator.classList.remove('hidden');
      }
    }

    function updateEditCopyModeStatus() {
      const copyIndicator = document.getElementById('editCopyModeIndicator');
      const reencodeIndicator = document.getElementById('editReencodeIndicator');

      if (!currentEditVideoCompatibility) {
        copyIndicator.classList.add('hidden');
        reencodeIndicator.classList.add('hidden');
        return;
      }

      if (currentEditVideoCompatibility.compatible) {
        // Check if current settings would still use copy mode
        const willUseCopyMode = checkEditCurrentSettingsCompatibility();

        if (willUseCopyMode) {
          copyIndicator.classList.remove('hidden');
          reencodeIndicator.classList.add('hidden');
        } else {
          copyIndicator.classList.add('hidden');
          reencodeIndicator.classList.remove('hidden');
        }
      } else {
        copyIndicator.classList.add('hidden');
        reencodeIndicator.classList.remove('hidden');
      }
    }

    function checkCurrentSettingsCompatibility() {
      if (!currentVideoCompatibility || !currentVideoCompatibility.compatible) {
        return false;
      }

      const settings = currentVideoCompatibility.settings;
      const currentBitrate = parseInt(document.getElementById('bitrateSelect').value);
      const currentFps = parseInt(document.getElementById('fpsSelect').value);

      return settings.bitrateOptions.includes(currentBitrate) &&
             settings.fpsOptions.includes(currentFps);
    }

    function checkEditCurrentSettingsCompatibility() {
      if (!currentEditVideoCompatibility || !currentEditVideoCompatibility.compatible) {
        return false;
      }

      const settings = currentEditVideoCompatibility.settings;
      const currentBitrate = parseInt(document.getElementById('editBitrate').value);
      const currentFps = parseInt(document.getElementById('editFps').value);

      return settings.bitrateOptions.includes(currentBitrate) &&
             settings.fpsOptions.includes(currentFps);
    }

    function showCopyModeWarning(reason) {
      // Create or show warning message
      let warningDiv = document.getElementById('copyModeWarning');
      if (!warningDiv) {
        warningDiv = document.createElement('div');
        warningDiv.id = 'copyModeWarning';
        warningDiv.className = 'p-3 bg-orange-600/20 border border-orange-600/30 rounded-lg mb-4';
        warningDiv.innerHTML = `
          <div class="flex items-start">
            <div class="flex-shrink-0">
              <i class="ti ti-alert-triangle text-orange-500 text-lg"></i>
            </div>
            <div class="ml-3">
              <h4 class="text-sm font-medium text-orange-400">Copy Mode Tidak Tersedia</h4>
              <p class="text-sm text-gray-300 mt-1">${reason}. Streaming akan menggunakan re-encoding mode.</p>
            </div>
          </div>
        `;

        const advancedContent = document.getElementById('advancedSettingsContent');
        advancedContent.insertBefore(warningDiv, advancedContent.firstChild);
      }
      warningDiv.classList.remove('hidden');
    }

    function hideCopyModeWarning() {
      const warningDiv = document.getElementById('copyModeWarning');
      if (warningDiv) {
        warningDiv.classList.add('hidden');
      }
    }

    function updateEditAdvancedSettingsForCopyMode(compatibilityData) {
      if (!compatibilityData.compatible) {
        // Video not compatible with copy mode
        showEditCopyModeWarning(compatibilityData.reason);
        return;
      }

      const settings = compatibilityData.settings;

      // Update bitrate options for edit form
      const bitrateSelect = document.getElementById('editBitrate');
      if (bitrateSelect && settings.bitrateOptions) {
        updateEditSelectOptions(bitrateSelect, settings.bitrateOptions, 'kbps');
      }

      // Update FPS options for edit form
      const fpsSelect = document.getElementById('editFps');
      if (fpsSelect && settings.fpsOptions) {
        updateEditSelectOptions(fpsSelect, settings.fpsOptions, 'FPS');
      }

      // Update resolution options for edit form
      const resolutionSelect = document.getElementById('editResolutionSelect');
      if (resolutionSelect && settings.compatibleResolutions) {
        updateEditResolutionOptions(resolutionSelect, settings.compatibleResolutions);
      }

      hideEditCopyModeWarning();
    }

    function updateEditSelectOptions(selectElement, options, suffix) {
      // Store current value
      const currentValue = selectElement.value;

      // Clear existing options
      selectElement.innerHTML = '';

      // Add compatible options
      options.forEach(option => {
        const optionElement = document.createElement('option');
        optionElement.value = option;
        optionElement.textContent = `${option} ${suffix}`;
        selectElement.appendChild(optionElement);
      });

      // Restore value if still compatible, otherwise select first option
      if (options.includes(parseInt(currentValue))) {
        selectElement.value = currentValue;
      } else {
        selectElement.selectedIndex = 0;
      }
    }

    function updateEditResolutionOptions(selectElement, resolutions) {
      // Store current value
      const currentValue = selectElement.value;

      // Clear existing options
      selectElement.innerHTML = '';

      // Add compatible resolutions
      resolutions.forEach((res, index) => {
        const optionElement = document.createElement('option');
        // Handle both string and object formats
        if (typeof res === 'string') {
          // Simple string format like "1280x720"
          const resolutionValue = res.match(/(\d+)x(\d+)/)?.[0] || res;
          const resolutionLabel = res.includes('x') ? res : `${res}p`;
          const height = resolutionValue.split('x')[1] || res;
          optionElement.value = height; // Use height as value for consistency
          optionElement.setAttribute('data-horizontal', resolutionValue);
          optionElement.setAttribute('data-vertical', resolutionValue.split('x').reverse().join('x'));
          optionElement.textContent = resolutionLabel;
        } else if (res && res.value && res.label) {
          // Object format like { value: "1280x720", label: "720p HD" }
          const [width, height] = res.value.split('x');
          optionElement.value = height; // Use height as value for consistency
          optionElement.setAttribute('data-horizontal', res.value);
          optionElement.setAttribute('data-vertical', `${height}x${width}`);

          // Add re-encoding indicator if needed
          let labelText = res.label;
          if (res.requiresReencoding) {
            labelText += ' (Re-encode)';
          }
          optionElement.textContent = labelText;
        }
        selectElement.appendChild(optionElement);
      });

      // Restore value if still compatible, otherwise select first option
      const compatibleValues = Array.from(selectElement.options).map(opt => opt.value);
      if (compatibleValues.includes(currentValue)) {
        selectElement.value = currentValue;
      } else {
        selectElement.selectedIndex = 0;
      }
      // Only call updateEditResolutionDisplay if elements are ready
      setTimeout(() => {
        updateEditResolutionDisplay();
      }, 10);
    }

    function showEditCopyModeWarning(reason) {
      // Create or show warning message for edit form
      let warningDiv = document.getElementById('editCopyModeWarning');
      if (!warningDiv) {
        warningDiv = document.createElement('div');
        warningDiv.id = 'editCopyModeWarning';
        warningDiv.className = 'p-4 bg-orange-600/20 border border-orange-600/30 rounded-lg';
        warningDiv.innerHTML = `
          <div class="flex items-start">
            <div class="flex-shrink-0">
              <i class="ti ti-alert-triangle text-orange-500 text-lg"></i>
            </div>
            <div class="ml-3">
              <h4 class="text-sm font-medium text-orange-400">Copy Mode Not Available</h4>
              <p class="text-sm text-gray-300 mt-1">${reason}. Streaming akan menggunakan re-encoding mode.</p>
            </div>
          </div>
        `;

        const editAdvancedContent = document.getElementById('editAdvancedSettingsContent');
        editAdvancedContent.insertBefore(warningDiv, editAdvancedContent.firstChild);
      }
      warningDiv.classList.remove('hidden');
    }

    function hideEditCopyModeWarning() {
      const warningDiv = document.getElementById('editCopyModeWarning');
      if (warningDiv) {
        warningDiv.classList.add('hidden');
      }
    }

    // Resolution and Orientation Handlers
    let currentOrientation = 'horizontal';
    // currentEditOrientation already declared above

    function initializeResolutionHandlers() {
      // Initialize resolution display for create stream
      updateResolutionDisplay();

      // Add event listener for resolution select change
      const resolutionSelect = document.getElementById('resolutionSelect');
      if (resolutionSelect) {
        resolutionSelect.addEventListener('change', updateResolutionDisplay);
      }
    }

    function setVideoOrientation(orientation) {
      currentOrientation = orientation;

      // Update button states
      const horizontalBtn = document.querySelector('[onclick="setVideoOrientation(\'horizontal\')"]');
      const verticalBtn = document.querySelector('[onclick="setVideoOrientation(\'vertical\')"]');

      if (horizontalBtn && verticalBtn) {
        // Reset both buttons
        horizontalBtn.classList.remove('active-orientation', 'bg-primary');
        horizontalBtn.classList.add('bg-dark-700');
        verticalBtn.classList.remove('active-orientation', 'bg-primary');
        verticalBtn.classList.add('bg-dark-700');

        // Set active button
        if (orientation === 'horizontal') {
          horizontalBtn.classList.add('active-orientation', 'bg-primary');
          horizontalBtn.classList.remove('bg-dark-700');
        } else {
          verticalBtn.classList.add('active-orientation', 'bg-primary');
          verticalBtn.classList.remove('bg-dark-700');
        }
      }

      // Update resolution display
      updateResolutionDisplay();
    }

    function updateResolutionDisplay() {
      const select = document.getElementById('resolutionSelect');
      const currentResolutionSpan = document.getElementById('currentResolution');

      if (select && currentResolutionSpan && select.options.length > 0 && select.selectedIndex >= 0) {
        const selected = select.options[select.selectedIndex];
        if (selected) {
          // Use currentOrientation if available, otherwise default to 'horizontal'
          const orientation = currentOrientation || 'horizontal';
          const resValue = selected.getAttribute(`data-${orientation}`);
          if (resValue) {
            currentResolutionSpan.textContent = resValue;
          } else {
            // Fallback: try to extract resolution from option text or value
            const optionText = selected.textContent || selected.value;
            if (optionText && optionText.includes('x')) {
              currentResolutionSpan.textContent = optionText;
            } else if (selected.value && !isNaN(selected.value)) {
              // If value is just height (like "720"), construct resolution
              const height = selected.value;
              const width = orientation === 'vertical' ?
                (height == '720' ? '720' : height == '1080' ? '1080' : height) :
                (height == '720' ? '1280' : height == '1080' ? '1920' : height == '480' ? '854' : height == '360' ? '640' : height == '1440' ? '2560' : height == '2160' ? '3840' : height);
              const resolution = orientation === 'vertical' ? `${height}x${width}` : `${width}x${height}`;
              currentResolutionSpan.textContent = resolution;
            } else {
              currentResolutionSpan.textContent = '1280x720'; // Default fallback
            }
          }
        }
      }
    }

    function updateEditResolutionDisplay() {
      const select = document.getElementById('editResolutionSelect');
      const currentResolutionSpan = document.getElementById('editCurrentResolution');

      if (select && currentResolutionSpan && select.options.length > 0 && select.selectedIndex >= 0) {
        const selected = select.options[select.selectedIndex];
        if (selected) {
          // Use currentEditOrientation if available, otherwise default to 'horizontal'
          const orientation = currentEditOrientation || 'horizontal';
          const resValue = selected.getAttribute(`data-${orientation}`);
          if (resValue) {
            currentResolutionSpan.textContent = resValue;
          } else {
            // Fallback: try to extract resolution from option text or value
            const optionText = selected.textContent || selected.value;
            if (optionText && optionText.includes('x')) {
              currentResolutionSpan.textContent = optionText;
            } else if (selected.value && !isNaN(selected.value)) {
              // If value is just height (like "720"), construct resolution
              const height = selected.value;
              const width = orientation === 'vertical' ?
                (height == '720' ? '720' : height == '1080' ? '1080' : height == '1440' ? '1440' : height == '2160' ? '2160' : height) :
                (height == '720' ? '1280' : height == '1080' ? '1920' : height == '480' ? '854' : height == '360' ? '640' : height == '1440' ? '2560' : height == '2160' ? '3840' : height);
              const resolution = orientation === 'vertical' ? `${height}x${width}` : `${width}x${height}`;
              currentResolutionSpan.textContent = resolution;
            } else {
              currentResolutionSpan.textContent = '1280x720'; // Default fallback
            }
          }
        }
      }
    }

    // Make functions globally available
    window.setVideoOrientation = setVideoOrientation;
    window.updateResolutionDisplay = updateResolutionDisplay;

    document.addEventListener('DOMContentLoaded', function () {
      setupAdvancedSettings('advancedSettingsToggle', 'advancedSettingsContent');
      setupAdvancedSettings('editAdvancedSettingsToggle', 'editAdvancedSettingsContent');
    });
    function setupAdvancedSettings(toggleId, contentId) {
      const toggle = document.getElementById(toggleId);
      const content = document.getElementById(contentId);
      if (toggle && content) {
        toggle.addEventListener('click', function () {
          content.classList.toggle('hidden');
          const icon = this.querySelector('i');
          if (content.classList.contains('hidden')) {
            icon.style.transform = '';
          } else {
            icon.style.transform = 'rotate(180deg)';
          }
        });
      }
    }
    function startLiveTimers() {
      setInterval(() => {
        document.querySelectorAll('.live-duration').forEach(el => {
          const startTime = new Date(el.dataset.startTime);
          const now = new Date();
          const durationMs = now - startTime;
          const hours = Math.floor(durationMs / (1000 * 60 * 60)).toString().padStart(2, '0');
          const minutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60)).toString().padStart(2, '0');
          const seconds = Math.floor((durationMs % (1000 * 60)) / 1000).toString().padStart(2, '0');
          el.textContent = `${hours}:${minutes}:${seconds}`;
        });
      }, 1000);
    }
    function updateServerTime() {
      fetch('/api/server-time')
        .then(response => response.json())
        .then(data => {
          if (data.formattedTime) {
            const timeDisplay = `Server time: ${data.formattedTime}`;
            const createModalDisplay = document.getElementById('serverTimeDisplay');
            if (createModalDisplay) {
              createModalDisplay.textContent = timeDisplay;
            }
            const editModalDisplay = document.getElementById('editServerTimeDisplay');
            if (editModalDisplay) {
              editModalDisplay.textContent = timeDisplay;
            }
          }
        })
        .catch(error => {
          // console.error('Error fetching server time:', error); // Cleaned for production
        });
    }
    document.addEventListener('DOMContentLoaded', function () {
      updateServerTime();
      // Update server time every 30 seconds instead of every second
      setInterval(updateServerTime, 30000);
    });
    document.addEventListener('DOMContentLoaded', function () {
      const searchInput = document.querySelector('input[placeholder="Search streams..."]');
      if (searchInput) {
        searchInput.addEventListener('input', function () {
          const searchTerm = this.value.toLowerCase().trim();
          const streamRows = document.querySelectorAll('table tbody tr:not(#empty-state)');
          const streamCards = document.querySelectorAll('.block.md\\:hidden.space-y-4 > div');
          streamRows.forEach(row => {
            const streamTitle = row.querySelector('.text-sm.font-medium')?.textContent.toLowerCase() || '';
            const isMatch = streamTitle.includes(searchTerm);
            row.style.display = isMatch ? '' : 'none';
          });
          streamCards.forEach(card => {
            const streamTitle = card.querySelector('.font-medium')?.textContent.toLowerCase() || '';
            const isMatch = streamTitle.includes(searchTerm);
            card.style.display = isMatch ? '' : 'none';
          });
          const visibleRows = [...streamRows].filter(row => row.style.display !== 'none');
          const visibleCards = [...streamCards].filter(card => card.style.display !== 'none');
          const emptyStateRow = document.getElementById('empty-state');
          if (visibleRows.length === 0 && emptyStateRow) {
            emptyStateRow.style.display = 'table-row';
            const emptyStateMessage = emptyStateRow.querySelector('p.text-gray-500');
            if (emptyStateMessage) {
              emptyStateMessage.textContent = searchTerm ? 'No streams match your search' : 'Create your first stream to start broadcasting to your audience';
            }
          } else if (emptyStateRow) {
            emptyStateRow.style.display = 'none';
          }
          if (visibleCards.length === 0) {
            const mobileContainer = document.querySelector('.block.md\\:hidden.space-y-4');
            if (mobileContainer && visibleCards.length === 0 && streamCards.length > 0) {
              const searchMessage = searchTerm ? 'No streams match your search' : 'Create your first stream to start broadcasting';
              mobileContainer.innerHTML = `
            <div class="bg-gray-800 rounded-lg p-6 text-center">
              <div class="flex flex-col items-center">
                <div class="w-16 h-16 rounded-full bg-dark-700 flex items-center justify-center mb-4">
                  <i class="ti ti-search-off text-gray-500 text-2xl"></i>
                </div>
                <p class="text-gray-400 font-medium mb-2">${searchMessage}</p>
              </div>
            </div>
          `;
            }
          }
        });
      }
    });

    // Function to clear failed stream status
    async function clearFailedStream(streamId) {
      if (!streamId) return;

      const confirmed = await notifications.confirm(
        'Clear error status for this stream? This will reset the stream to offline status.',
        'Clear Error Status',
        {
          confirmText: 'Clear Status',
          type: 'warning'
        }
      );

      if (!confirmed) {
        return;
      }

      const actionBtns = document.querySelectorAll(`[data-stream-id="${streamId}"] .action-btn`);
      const originalBtnStates = [];

      actionBtns.forEach(btn => {
        originalBtnStates.push({
          element: btn,
          html: btn.innerHTML,
          disabled: btn.disabled
        });
        btn.innerHTML = '<i class="ti ti-loader animate-spin"></i> Clearing...';
        btn.disabled = true;
      });

      function restoreButtons() {
        originalBtnStates.forEach(state => {
          state.element.innerHTML = state.html;
          state.element.disabled = state.disabled;
        });
      }

      fetch(`/api/streams/${streamId}/clear-failed`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      })
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            notifications.success('Status Cleared', data.message);
            // Refresh the page to show updated status
            setTimeout(() => {
              window.location.reload();
            }, 1000);
          } else {
            notifications.error('Clear Failed', data.error || 'Failed to clear error status');
            restoreButtons();
          }
        })
        .catch(error => {
          console.error('Error:', error);
          notifications.error('Network Error', 'An error occurred while clearing error status');
          restoreButtons();
        });
    }

    // Function to fix stream status inconsistency
    async function fixStreamStatus(streamId) {
      if (!streamId) return;

      const confirmed = await notifications.confirm(
        'Fix status inconsistency for this stream? This will synchronize the stream status.',
        'Fix Stream Status',
        {
          confirmText: 'Fix Status',
          type: 'warning'
        }
      );

      if (!confirmed) {
        return;
      }

      const actionBtns = document.querySelectorAll(`[data-stream-id="${streamId}"] .action-btn`);
      const originalBtnStates = [];

      actionBtns.forEach(btn => {
        originalBtnStates.push({
          element: btn,
          html: btn.innerHTML,
          disabled: btn.disabled
        });
        btn.innerHTML = '<i class="ti ti-loader animate-spin"></i> Fixing...';
        btn.disabled = true;
      });

      function restoreButtons() {
        originalBtnStates.forEach(state => {
          state.element.innerHTML = state.html;
          state.element.disabled = state.disabled;
        });
      }

      fetch(`/api/streams/${streamId}/fix-status`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      })
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            notifications.success('Status Fixed', data.message || 'Stream status fixed successfully');
            // Refresh the page to show updated status
            setTimeout(() => {
              window.location.reload();
            }, 1000);
          } else {
            notifications.error('Fix Failed', data.error || 'Failed to fix stream status');
            restoreButtons();
          }
        })
        .catch(error => {
          console.error('Error:', error);
          notifications.error('Network Error', 'An error occurred while fixing stream status');
          restoreButtons();
        });
    }

    // Function to toggle stream key visibility for new stream modal
    function toggleStreamKeyVisibility() {
      const streamKeyInput = document.getElementById('streamKey');
      const toggleIcon = document.getElementById('streamKeyToggle');

      if (streamKeyInput.type === 'password') {
        streamKeyInput.type = 'text';
        toggleIcon.className = 'ti ti-eye-off';
      } else {
        streamKeyInput.type = 'password';
        toggleIcon.className = 'ti ti-eye';
      }
    }

    // Real-time RTMP validation
    let validationTimeout;

    function validateRtmpConfig() {
      const rtmpUrl = document.getElementById('rtmpUrl').value.trim();
      const streamKey = document.getElementById('streamKey').value.trim();

      // Clear previous timeout
      if (validationTimeout) {
        clearTimeout(validationTimeout);
      }

      // Hide validation if both fields are empty
      if (!rtmpUrl && !streamKey) {
        hideValidation();
        return;
      }

      // Debounce validation
      validationTimeout = setTimeout(async () => {
        if (rtmpUrl && streamKey) {
          await performValidation(rtmpUrl, streamKey);
        }
      }, 500);
    }

    async function performValidation(rtmpUrl, streamKey) {
      try {
        const response = await fetch('/api/validate-rtmp', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ rtmpUrl, streamKey })
        });

        const data = await response.json();

        if (data.success) {
          displayValidation(data.validation);
        } else {
          showValidationError(data.error);
        }
      } catch (error) {
        // console.error('Validation error:', error); // Cleaned for production
        showValidationError('Failed to validate RTMP configuration');
      }
    }

    function displayValidation(validation) {
      const container = document.getElementById('rtmpValidationContainer');
      const errorsDiv = document.getElementById('rtmpValidationErrors');
      const warningsDiv = document.getElementById('rtmpValidationWarnings');
      const platformHelp = document.getElementById('platformHelp');

      // Clear previous content
      errorsDiv.innerHTML = '';
      warningsDiv.innerHTML = '';
      errorsDiv.classList.add('hidden');
      warningsDiv.classList.add('hidden');
      platformHelp.classList.add('hidden');

      // Show errors
      if (validation.errors && validation.errors.length > 0) {
        validation.errors.forEach(error => {
          const errorDiv = document.createElement('div');
          errorDiv.className = 'flex items-center text-sm text-red-400';
          errorDiv.innerHTML = `<i class="ti ti-alert-circle mr-2"></i>${error}`;
          errorsDiv.appendChild(errorDiv);
        });
        errorsDiv.classList.remove('hidden');
      }

      // Show warnings
      if (validation.warnings && validation.warnings.length > 0) {
        validation.warnings.forEach(warning => {
          const warningDiv = document.createElement('div');
          warningDiv.className = 'flex items-center text-sm text-yellow-400';
          warningDiv.innerHTML = `<i class="ti ti-alert-triangle mr-2"></i>${warning}`;
          warningsDiv.appendChild(warningDiv);
        });
        warningsDiv.classList.remove('hidden');
      }

      // Show platform help
      if (validation.platform && validation.platformHelp) {
        const help = validation.platformHelp;
        document.getElementById('platformName').textContent = `${validation.platform.toUpperCase()} Configuration`;
        document.getElementById('platformInstructions').textContent = help.instructions;
        document.getElementById('platformKeyFormat').textContent = `Key format: ${help.keyFormat}`;

        // Show suggestions
        const suggestionsDiv = document.getElementById('platformSuggestions');
        suggestionsDiv.innerHTML = '';
        if (validation.suggestions && validation.suggestions.length > 0) {
          validation.suggestions.forEach(suggestion => {
            const suggestionDiv = document.createElement('div');
            suggestionDiv.className = 'text-xs text-primary';
            suggestionDiv.innerHTML = `• ${suggestion}`;
            suggestionsDiv.appendChild(suggestionDiv);
          });
        }

        platformHelp.classList.remove('hidden');
      }

      // Show container if there's any content
      if (!errorsDiv.classList.contains('hidden') ||
          !warningsDiv.classList.contains('hidden') ||
          !platformHelp.classList.contains('hidden')) {
        container.classList.remove('hidden');
      } else {
        container.classList.add('hidden');
      }
    }

    function showValidationError(message) {
      const container = document.getElementById('rtmpValidationContainer');
      const errorsDiv = document.getElementById('rtmpValidationErrors');

      errorsDiv.innerHTML = `
        <div class="flex items-center text-sm text-red-400">
          <i class="ti ti-alert-circle mr-2"></i>${message}
        </div>
      `;
      errorsDiv.classList.remove('hidden');
      container.classList.remove('hidden');
    }

    function hideValidation() {
      document.getElementById('rtmpValidationContainer').classList.add('hidden');
      document.getElementById('platformHelp').classList.add('hidden');
    }

    // Add event listeners for real-time validation
    document.addEventListener('DOMContentLoaded', function() {
      const rtmpUrlInput = document.getElementById('rtmpUrl');
      const streamKeyInput = document.getElementById('streamKey');

      if (rtmpUrlInput && streamKeyInput) {
        rtmpUrlInput.addEventListener('input', validateRtmpConfig);
        streamKeyInput.addEventListener('input', validateRtmpConfig);

        // Also validate when platform is selected
        document.addEventListener('click', function(e) {
          if (e.target.closest('.platform-option')) {
            setTimeout(validateRtmpConfig, 100);
          }
        });
      }
    });

    // Load Balancer Notification Functions
    function showLoadBalancerNotification(message) {
      const notification = document.getElementById('loadBalancerNotification');
      const messageElement = document.getElementById('notificationMessage');

      messageElement.textContent = message;
      notification.classList.remove('hidden');

      // Auto-hide after 10 seconds
      setTimeout(() => {
        hideNotification();
      }, 10000);
    }

    function hideNotification() {
      document.getElementById('loadBalancerNotification').classList.add('hidden');
    }

    // Monitor load balancer status for notifications
    let lastQualityLevel = null;

    async function checkLoadBalancerStatus() {
      try {
        const response = await fetch('/api/load-balancer/metrics');
        const metrics = await response.json();

        if (lastQualityLevel && lastQualityLevel !== metrics.currentQualityLevel) {
          const qualityMap = {
            'NORMAL': '720p+ quality (CPU normal)',
            'MEDIUM': '480p quality (CPU moderate)',
            'LOW': '360p quality (CPU high)',
            'MINIMAL': '240p quality (CPU critical)'
          };

          const message = `Stream quality automatically adjusted to ${qualityMap[metrics.currentQualityLevel] || metrics.currentQualityLevel}`;
          showLoadBalancerNotification(message);
        }

        lastQualityLevel = metrics.currentQualityLevel;
      } catch (error) {
        // Silently fail - load balancer might not be available
      }
    }

    // Check load balancer status every 60 seconds (reduced frequency)
    setInterval(checkLoadBalancerStatus, 60000);

    // Initial check
    setTimeout(checkLoadBalancerStatus, 2000);

    // Schedule Time Validation Function
    function validateScheduleTime(scheduleTime, scheduleTimezone, errorElementId) {
      const errorElement = document.getElementById(errorElementId);
      
      if (!scheduleTime) {
        errorElement.textContent = '';
        errorElement.classList.add('hidden');
        return true;
      }
      
      // Backend selalu menggunakan Asia/Jakarta, tapi frontend validasi menggunakan timezone yang dipilih user
      // untuk memberikan feedback yang akurat
      const selectedTimezone = scheduleTimezone || 'Asia/Jakarta';
      
      try {
        // Konversi menggunakan timezone yang dipilih user untuk validasi frontend
        const zonedTime = moment.tz(scheduleTime, "YYYY-MM-DDTHH:mm", selectedTimezone);
        const utcTime = zonedTime.utc();
        
        // Get current time
        const now = moment();
        const minimumTime = moment().add(1, 'minute');
        
        console.log(`[Frontend Schedule Validation] Input: ${scheduleTime} (${selectedTimezone} - User Selected)`);
        console.log(`[Frontend Schedule Validation] UTC: ${utcTime.toISOString()}`);
        console.log(`[Frontend Schedule Validation] Current: ${now.toISOString()}`);
        
        if (utcTime.isBefore(minimumTime)) {
          errorElement.textContent = 'Waktu jadwal harus minimal 1 menit dari sekarang';
          errorElement.classList.remove('hidden');
          
          // Highlight the input field
          const inputElement = document.getElementById(errorElementId.replace('Error', ''));
          if (inputElement) {
            inputElement.classList.add('border-red-500');
            inputElement.classList.remove('border-gray-600');
          }
          
          return false;
        }
        
        errorElement.textContent = '';
        errorElement.classList.add('hidden');
        
        // Remove highlight from input field
        const inputElement = document.getElementById(errorElementId.replace('Error', ''));
        if (inputElement) {
          inputElement.classList.remove('border-red-500');
          inputElement.classList.add('border-gray-600');
        }
        
        return true;
      } catch (error) {
        console.error('Error validating schedule time:', error);
        errorElement.textContent = 'Format waktu tidak valid';
        errorElement.classList.remove('hidden');
        return false;
      }
    }

    // Timezone Management Functions
    let availableTimezones = [];
    let defaultTimezone = 'UTC';

    // Load available timezones
    async function loadTimezones() {
      try {
        const response = await fetch('/api/timezones');
        const data = await response.json();

        if (data.success) {
          availableTimezones = data.timezones;
          defaultTimezone = data.defaultTimezone;

          // Populate timezone selectors
          populateTimezoneSelector('scheduleTimezone');
          populateTimezoneSelector('editScheduleTimezone');
        }
      } catch (error) {
        console.error('Error loading timezones:', error);
        // Fallback to basic timezone options
        availableTimezones = [
          { value: 'UTC', label: 'UTC - Coordinated Universal Time', offset: '+00:00' },
          { value: 'Asia/Jakarta', label: 'WIB - Jakarta, Bandung, Surabaya', offset: '+07:00' },
          { value: 'Asia/Makassar', label: 'WITA - Makassar, Denpasar, Balikpapan', offset: '+08:00' },
          { value: 'Asia/Jayapura', label: 'WIT - Jayapura, Manokwari', offset: '+09:00' }
        ];
        populateTimezoneSelector('scheduleTimezone');
        populateTimezoneSelector('editScheduleTimezone');
      }
    }

    // Enhanced populate timezone selector with search functionality
    function populateTimezoneSelector(dropdownId, searchInputId, hiddenInputId, searchTerm = '') {
      const dropdown = document.getElementById(dropdownId);
      if (!dropdown) return;

      dropdown.innerHTML = '';

      // Add search input inside dropdown
      const searchContainer = document.createElement('div');
      searchContainer.className = 'p-2 border-b border-gray-600/50 sticky top-0 bg-dark-700';
      
      const searchInput = document.createElement('input');
      searchInput.type = 'text';
      searchInput.className = 'w-full bg-dark-800 text-white px-3 py-2 rounded text-sm focus:outline-none focus:ring-1 focus:ring-primary border border-gray-700';
      searchInput.placeholder = 'Type to search timezone...';
      searchInput.value = searchTerm;
      
      searchContainer.appendChild(searchInput);
      dropdown.appendChild(searchContainer);

      // Options container
      const optionsContainer = document.createElement('div');
      optionsContainer.className = 'max-h-48 overflow-y-auto';
      dropdown.appendChild(optionsContainer);

      const filteredTimezones = availableTimezones.filter(timezone =>
        timezone.label.toLowerCase().includes(searchTerm.toLowerCase())
      );

      if (filteredTimezones.length === 0) {
        const noResultDiv = document.createElement('div');
        noResultDiv.className = 'p-3 text-sm text-gray-400 text-center';
        noResultDiv.innerHTML = '<i class="ti ti-search-off text-lg mb-1 block"></i>No timezone found';
        optionsContainer.appendChild(noResultDiv);
        return;
      }

      filteredTimezones.forEach(timezone => {
        const optionDiv = document.createElement('div');
        optionDiv.className = 'p-3 text-sm text-white hover:bg-primary cursor-pointer border-b border-gray-700/30 last:border-b-0 transition-colors';
        optionDiv.innerHTML = `
          <div class="font-medium">${timezone.label}</div>
          <div class="text-xs text-gray-400 mt-1">${timezone.value}</div>
        `;
        optionDiv.dataset.value = timezone.value;
        optionDiv.addEventListener('click', () => {
          document.getElementById(searchInputId).value = timezone.label;
          document.getElementById(hiddenInputId).value = timezone.value;
          dropdown.classList.add('hidden');
          
          // Rotate chevron back
          const chevron = document.getElementById(searchInputId.replace('Search', 'Chevron'));
          if (chevron) {
            chevron.style.transform = 'translateY(-50%) rotate(0deg)';
          }
        });
        optionsContainer.appendChild(optionDiv);
      });

      // Handle search input
      searchInput.addEventListener('input', (e) => {
        populateTimezoneSelector(dropdownId, searchInputId, hiddenInputId, e.target.value);
      });

      // Focus search input when dropdown opens
      setTimeout(() => searchInput.focus(), 100);
    }

    // Update schedule time display based on timezone
    function updateScheduleTimeDisplay(datetimeInput, timezoneSelect, displayElement) {
      const datetime = datetimeInput.value;
      const timezone = timezoneSelect.value;

      if (!datetime || !timezone) return;

      try {
        // Convert to display format with timezone
        const localDate = new Date(datetime);
        const options = {
          year: 'numeric',
          month: 'short',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit',
          timeZone: timezone,
          timeZoneName: 'short'
        };

        const formattedTime = localDate.toLocaleString('en-US', options);
        if (displayElement) {
          displayElement.textContent = `Scheduled: ${formattedTime}`;
        }
      } catch (error) {
        console.error('Error updating schedule time display:', error);
      }
    }

    // Handle timezone change
    function handleTimezoneChange(timezoneSelectId, datetimeInputId) {
      const timezoneSelect = document.getElementById(timezoneSelectId);
      const datetimeInput = document.getElementById(datetimeInputId);

      if (!timezoneSelect || !datetimeInput) return;

      timezoneSelect.addEventListener('change', function() {
        // Update any related displays
        // console.log(`Timezone changed to: ${this.value}`); // Removed for production
      });
    }

    // Initialize timezone functionality
    function initializeTimezones() {
      loadTimezones().then(() => {
        // Set default timezone values on load
        const scheduleHidden = document.getElementById('scheduleTimezone');
        const scheduleSearch = document.getElementById('scheduleTimezoneSearch');
        if (scheduleHidden && scheduleSearch) {
          scheduleHidden.value = defaultTimezone;
          const defaultTz = availableTimezones.find(tz => tz.value === defaultTimezone);
          if (defaultTz) {
            scheduleSearch.value = defaultTz.label;
          }
        }
        
        const editHidden = document.getElementById('editScheduleTimezone');
        const editSearch = document.getElementById('editScheduleTimezoneSearch');
        if (editHidden && editSearch) {
          editHidden.value = defaultTimezone;
          const defaultTz = availableTimezones.find(tz => tz.value === defaultTimezone);
          if (defaultTz) {
            editSearch.value = defaultTz.label;
          }
        }
        
        // Set up event listeners
        handleTimezoneChange('scheduleTimezone', 'scheduleTime');
        handleTimezoneChange('editScheduleTimezone', 'editScheduleTime');

        // Enhanced timezone functionality with improved dropdown behavior
        const scheduleDropdown = document.getElementById('scheduleTimezoneDropdown');
        const scheduleChevron = document.getElementById('scheduleTimezoneChevron');

        if (scheduleSearch && scheduleDropdown && scheduleChevron) {
          // Make input clickable to open dropdown
          scheduleSearch.addEventListener('click', () => {
            if (scheduleDropdown.classList.contains('hidden')) {
              populateTimezoneSelector('scheduleTimezoneDropdown', 'scheduleTimezoneSearch', 'scheduleTimezone', '');
              scheduleDropdown.classList.remove('hidden');
              scheduleChevron.style.transform = 'translateY(-50%) rotate(180deg)';
              scheduleSearch.focus();
            } else {
              scheduleDropdown.classList.add('hidden');
              scheduleChevron.style.transform = 'translateY(-50%) rotate(0deg)';
            }
          });
        }

        // Handle keyboard navigation
        scheduleSearch.addEventListener('keydown', (e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            scheduleSearch.click();
          } else if (e.key === 'Escape') {
            scheduleDropdown.classList.add('hidden');
            scheduleChevron.style.transform = 'translateY(-50%) rotate(0deg)';
          }
        });

        // Same for edit modal
        const editDropdown = document.getElementById('editScheduleTimezoneDropdown');
        const editChevron = document.getElementById('editScheduleTimezoneChevron');
        
        if (editSearch) {
          editSearch.addEventListener('click', () => {
            if (editDropdown.classList.contains('hidden')) {
              populateTimezoneSelector('editScheduleTimezoneDropdown', 'editScheduleTimezoneSearch', 'editScheduleTimezone', '');
              editDropdown.classList.remove('hidden');
              editChevron.style.transform = 'translateY(-50%) rotate(180deg)';
            } else {
              editDropdown.classList.add('hidden');
              editChevron.style.transform = 'translateY(-50%) rotate(0deg)';
            }
          });
        }

        editSearch.addEventListener('keydown', (e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            editSearch.click();
          } else if (e.key === 'Escape') {
            editDropdown.classList.add('hidden');
            editChevron.style.transform = 'translateY(-50%) rotate(0deg)';
          }
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', (e) => {
          if (!document.getElementById('scheduleTimezoneWrapper').contains(e.target)) {
            scheduleDropdown.classList.add('hidden');
            scheduleChevron.style.transform = 'translateY(-50%) rotate(0deg)';
          }
          if (!document.getElementById('editScheduleTimezoneWrapper').contains(e.target)) {
            editDropdown.classList.add('hidden');
            editChevron.style.transform = 'translateY(-50%) rotate(0deg)';
          }
        });
      });
    }

    // Update edit modal to show timezone
    function updateEditModalWithTimezone(stream) {
      if (stream.schedule_time && stream.schedule_timezone) {
        // Convert UTC time to local timezone for display
        try {
          const utcDate = new Date(stream.schedule_time);
          const timezone = stream.schedule_timezone || 'UTC';

          // Convert UTC to local timezone for editing using Intl API
          const formatter = new Intl.DateTimeFormat('sv-SE', {
            timeZone: timezone,
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
          });

          const parts = formatter.formatToParts(utcDate);
          const year = parts.find(p => p.type === 'year').value;
          const month = parts.find(p => p.type === 'month').value;
          const day = parts.find(p => p.type === 'day').value;
          const hour = parts.find(p => p.type === 'hour').value;
          const minute = parts.find(p => p.type === 'minute').value;

          const formattedDate = `${year}-${month}-${day}T${hour}:${minute}`;
          document.getElementById('editScheduleTime').value = formattedDate;

          // Set timezone with proper display
          const timezoneHidden = document.getElementById('editScheduleTimezone');
          const timezoneSearch = document.getElementById('editScheduleTimezoneSearch');
          if (timezoneHidden && timezoneSearch) {
            timezoneHidden.value = timezone;
            // Find and display the timezone label
            const timezoneObj = availableTimezones.find(tz => tz.value === timezone);
            if (timezoneObj) {
              timezoneSearch.value = timezoneObj.label;
            } else {
              timezoneSearch.value = timezone; // Fallback to timezone value
            }
          }
        } catch (error) {
          console.error('Error setting edit modal timezone:', error);
        }
      }
    }

    // Initialize on DOM ready
    document.addEventListener('DOMContentLoaded', function() {
      initializeTimezones();
    });

    function openUpgradeModal() {
      const modal = document.getElementById('upgradeStreamModal');
      if (modal) {
        modal.style.cssText = `
          position: fixed !important;
          top: 0 !important;
          left: 0 !important;
          right: 0 !important;
          bottom: 0 !important;
          z-index: 99999 !important;
          background: rgba(0, 0, 0, 0.7) !important;
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;
          opacity: 1 !important;
          visibility: visible !important;
        `;
        
        const modalContainer = modal.querySelector('.modal-container');
        if (modalContainer) {
          modalContainer.style.cssText = `
            transform: scale(1) !important;
            opacity: 1 !important;
            visibility: visible !important;
          `;
        }
        
        modal.classList.remove('hidden');
        document.body.style.overflow = 'hidden';
      }
    }

    function closeUpgradeModal() {
      const modal = document.getElementById('upgradeStreamModal');
      if (modal) {
        modal.classList.add('hidden');
        modal.removeAttribute('style');
        document.body.style.overflow = '';
      }
    }

    document.addEventListener('DOMContentLoaded', function() {
      const slotsBar = document.getElementById('slots-bar');
      if (slotsBar) {
        const width = slotsBar.getAttribute('data-width');
        slotsBar.style.width = width + '%';
      }
    });
  </script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/moment-timezone/0.5.34/moment-timezone-with-data.min.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const timezones = moment.tz.names();
      const createTimezoneSelect = document.getElementById('displayTimezone');
      const editTimezoneSelect = document.getElementById('editDisplayTimezone');

      // Only populate timezone dropdowns if elements exist
      if (createTimezoneSelect && editTimezoneSelect) {
        // Populate timezone dropdowns
        timezones.forEach(tz => {
          const option = new Option(tz, tz);
          createTimezoneSelect.add(option.cloneNode(true));
          editTimezoneSelect.add(option);
        });

        // Set default to user's browser timezone
        const userTimezone = moment.tz.guess();
        createTimezoneSelect.value = userTimezone;
        editTimezoneSelect.value = userTimezone;
      }

      // Add event listeners
      const createScheduleTime = document.getElementById('scheduleTime');
      const editScheduleTime = document.getElementById('editScheduleTime');
      const createHelperText = document.getElementById('scheduleHelperText');
      const editHelperText = document.getElementById('editScheduleHelperText');

      function updateHelperText(datetimeInput, timezoneSelect, helperElement) {
        const scheduleTime = datetimeInput.value;
        const selectedTimezone = timezoneSelect.value || 'Asia/Jakarta';

        if (!scheduleTime) {
          helperElement.innerHTML = '<div class="text-gray-300"><%= t("streams.timezone.helper_text_empty") %></div>';
          return;
        }

        try {
          // Parse the datetime-local input as server time (WIB/Asia/Jakarta)
          const serverTime = moment.tz(scheduleTime, "YYYY-MM-DDTHH:mm", 'Asia/Jakarta');

          // Convert server time to user's selected timezone for display
          const userZonedTime = serverTime.clone().tz(selectedTimezone);

          // Convert to UTC for reference
          const utcTime = serverTime.clone().utc();

          // Format displays based on current locale
          const locale = '<%= locale %>';
          const timeFormat = locale === 'en' ? 'dddd, MMMM DD, YYYY [at] HH:mm' : 'dddd, DD MMMM YYYY [pukul] HH:mm';

          const serverDisplay = serverTime.format(timeFormat);
          const userDisplay = userZonedTime.format(timeFormat);
          const utcDisplay = utcTime.format(timeFormat) + ' UTC';

          // Get translated text (pre-rendered from server)
          const serverTimeText = `<%= t('streams.timezone.server_time') %>`;
          const selectedTimeText = `<%= t('streams.timezone.selected_time') %>`;
          const utcTimeText = `<%= t('streams.timezone.utc_time') %>`;
          const conversionNoteText = `<%= t('streams.timezone.conversion_note') %>`;

          let helperHTML = `
              <div class="text-sm text-gray-200">
                  <div class="mb-2 text-xs text-gray-400">${conversionNoteText}</div>
                  <div class="mb-1"><strong class="text-blue-400">${serverTimeText}:</strong> <span class="text-gray-100">${serverDisplay}</span></div>
          `;

          // If timezone is different from server, show conversion
          if (selectedTimezone !== 'Asia/Jakarta') {
              helperHTML += `<div class="mb-1"><strong class="text-green-400">${selectedTimeText} (${selectedTimezone}):</strong> <span class="text-gray-100">${userDisplay}</span></div>`;
          }

          helperHTML += `
                  <div class="mb-2"><strong class="text-purple-400">${utcTimeText}:</strong> <span class="text-gray-100">${utcDisplay}</span></div>
                  <div class="text-xs text-amber-400 bg-amber-400/10 px-2 py-1 rounded border border-amber-400/20">
                    💡 <%= locale === 'en' ? 'Enter time in WIB (server timezone). The display above shows how it appears in your selected timezone.' : 'Masukkan waktu dalam WIB (zona waktu server). Tampilan di atas menunjukkan bagaimana waktu tersebut terlihat di zona waktu pilihan Anda.' %>
                  </div>
              </div>
          `;

          helperElement.innerHTML = helperHTML;
        } catch (error) {
          helperElement.innerHTML = '<div class="text-red-400"><%= t("streams.timezone.helper_text_invalid") %></div>';
        }
      }

      createScheduleTime.addEventListener('input', () => updateHelperText(createScheduleTime, createTimezoneSelect, createHelperText));
      createTimezoneSelect.addEventListener('change', () => updateHelperText(createScheduleTime, createTimezoneSelect, createHelperText));
      editScheduleTime.addEventListener('input', () => updateHelperText(editScheduleTime, editTimezoneSelect, editHelperText));
      editTimezoneSelect.addEventListener('change', () => updateHelperText(editScheduleTime, editTimezoneSelect, editHelperText));
    });
  </script>