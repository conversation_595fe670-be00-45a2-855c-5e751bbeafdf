{"common": {"streamonpod": "StreamOnPod", "dashboard": "Dashboard", "streams": "Streams", "gallery": "Gallery", "history": "History", "plans": "Plans", "admin": "Admin", "settings": "Settings", "logout": "Sign Out", "login": "<PERSON><PERSON>", "register": "Register", "support": "Support", "help": "Help & Support", "profile": "Profile", "notifications": "Notifications", "loading": "Loading...", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "create": "Create", "update": "Update", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "search": "Search", "filter": "Filter", "sort": "Sort", "refresh": "Refresh", "export": "Export", "import": "Import", "download": "Download", "upload": "Upload", "submit": "Submit", "confirm": "Confirm", "yes": "Yes", "no": "No", "ok": "OK", "error": "Error", "success": "Success", "warning": "Warning", "info": "Information", "language": "Language", "english": "English", "indonesian": "Indonesian"}, "auth": {"login_title": "Sign In to StreamOnPod", "login_subtitle": "Welcome back! Please sign in to your account", "register_title": "Create Account", "register_subtitle": "Join StreamOnPod and start streaming", "email": "Email Address", "password": "Password", "confirm_password": "Confirm Password", "username": "Username", "full_name": "Full Name", "remember_me": "Remember me", "forgot_password": "Forgot password?", "dont_have_account": "Don't have an account?", "already_have_account": "Already have an account?", "sign_up": "Sign up", "sign_in": "Sign in", "create_account": "Create Account", "login_button": "Sign In", "register_button": "Create Account", "logout_success": "You have been logged out successfully", "login_required": "Please log in to access this page", "invalid_credentials": "Invalid email or password", "registration_success": "Account created successfully", "email_exists": "Email already exists", "username_exists": "Username already exists"}, "dashboard": {"title": "Stream Manager", "subtitle": "Manage your live streams and content", "welcome": "Welcome back", "create_stream": "Create New Stream", "create_stream_description": "Create your first stream to start broadcasting to your favorite platforms", "buy_plan": "Buy Plan", "upgrade_plan": "Upgrade Plan", "my_streams": "My Streams", "active_streams": "Active Streams", "total_streams": "Total Streams", "storage_used": "Storage Used", "streaming_quota": "Streaming Quota", "streaming_quota_desc": "Streaming slots that can be used simultaneously", "storage_used_desc": "Storage used to save videos uploaded to gallery", "no_streams": "No streams found", "stream_status": {"active": "Active", "inactive": "Inactive", "error": "Error", "starting": "Starting", "stopping": "Stopping", "scheduled": "Scheduled"}, "stream_actions": {"start": "Start", "stop": "Stop", "edit": "Edit", "delete": "Delete", "view": "View"}, "upgrade_to_stream": "Upgrade to Stream", "upgrade_tooltip": "Preview plan does not allow streaming. Upgrade to start streaming.", "no_slots_available": "No Slots Available", "streaming_limit_reached": "You have reached your streaming limit of {max} concurrent streams.", "new_stream": "New Stream", "create_stream_unlimited": "Create a new stream (Unlimited slots)", "create_stream_slots": "Create a new stream. You have {available} slots available.", "upgrade_modal": {"title": "Streaming Feature Available in Premium Plans", "message": "The streaming feature is not included in your current Preview Plan. Upgrade to unlock this feature and much more!", "cta": "View Plan Options"}}, "streams": {"title": "Stream Title", "description": "Description", "platform": "Platform", "stream_key": "Stream Key", "video_file": "Video File", "quality": "Quality", "orientation": "Orientation", "horizontal": "Horizontal", "vertical": "Vertical", "custom": "Custom", "youtube": "YouTube", "facebook": "Facebook", "tiktok": "TikTok", "instagram": "Instagram", "twitch": "Twitch", "create_new": "Create New Stream", "edit_stream": "Edit Stream", "delete_stream": "Delete Stream", "start_stream": "Start Stream", "stop_stream": "Stop Stream", "stream_url": "Stream URL", "rtmp_url": "RTMP URL", "advanced_settings": "Advanced Settings", "schedule_stream": "Schedule Stream", "auto_stop": "Auto Stop", "loop_video": "Loop Video", "bitrate": "Bitrate", "framerate": "Frame Rate", "resolution": "Resolution", "timezone": {"helper_text_empty": "Please fill in the schedule time to see the conversion preview.", "helper_text_invalid": "Invalid time format", "selected_time": "Your selected time", "server_time": "Server Time (WIB)", "utc_time": "UTC Time", "server_note": "💡 Server will use WIB time for scheduling", "display_in_timezone": "Display in Your Timezone", "schedule_time_label": "Schedule Date/Time (Server Time - WIB)", "conversion_note": "The time above will be converted to your selected timezone below for reference:"}}, "gallery": {"title": "Video Gallery", "subtitle": "Manage your videos for streaming", "total_videos": "Total Videos", "total_videos_desc": "Total videos stored in your gallery", "upload_video": "Upload Video", "import_from_drive": "Import from Drive", "my_videos": "My Videos", "video_name": "Video Name", "file_size": "File Size", "duration": "Duration", "upload_date": "Upload Date", "no_videos": "No videos found", "no_videos_yet": "No videos yet", "upload_first_video": "Upload your first video to get started", "upload_new": "Upload New Video", "delete_video": "Delete Video", "video_info": "Video Information", "supported_formats": "Supported formats: MP4, MOV (optimized for performance)", "supported_formats_short": "Supported formats: MP4, MOV only", "max_file_size": "Maximum file size", "upload_progress": "Upload Progress", "processing": "Processing video...", "search_videos": "Search videos...", "newest": "Newest", "oldest": "Oldest", "showing_videos": "Showing {{start}}-{{end}} of {{total}} videos", "drag_drop_files": "Drag and drop video files here", "click_to_browse": "Or click to browse", "select_files": "Select files", "uploading": "Uploading...", "google_drive_import": "Google Drive Import", "connect_google_drive": "Connect Google Drive", "api_key_needed": "API key needed to access Drive videos", "enter_api_key": "Enter your Google Drive API key", "get_api_key": "Get your API key from the", "google_cloud_console": "Google Cloud Console", "tutorial": "Tutorial", "save_api_key": "Save API Key", "import_from_google_drive": "Import from Google Drive", "enter_drive_link": "Enter a Google Drive link to your video", "drive_link_placeholder": "https://drive.google.com/file/d/...", "make_sure_shared": "Make sure the file is shared with", "anyone_with_link": "Anyone with the link", "import_video": "Import Video", "processing_import": "Processing...", "no_videos_search": "No videos found", "adjust_search": "Try adjusting your search criteria"}, "history": {"title": "Stream History", "subtitle": "View and manage your past streams", "search_placeholder": "Search history...", "all_platforms": "All Platforms", "stream_name": "Stream Name", "start_time": "Start Time", "end_time": "End Time", "duration": "Duration", "status": "Status", "platform": "Platform", "no_history": "No stream history found", "completed_streams_appear": "Your completed streams will appear here", "view_details": "View Details", "export_history": "Export History", "delete_confirm": "Are you sure you want to delete the history entry for \"{title}\"?", "delete_success": "History entry deleted successfully", "delete_error": "Error", "delete_error_general": "Failed to delete history entry", "no_matching_results": "No matching results found", "adjust_search_filter": "Try adjusting your search or filter"}, "subscription": {"title": "Subscription Plans", "current_plan": "Current Plan", "upgrade_plan": "Upgrade Plan", "downgrade_plan": "Downgrade Plan", "billing_cycle": "Billing Cycle", "monthly": "Monthly", "yearly": "Yearly", "features": "Features", "streaming_slots": "Streaming Slots", "storage_limit": "Storage Limit", "support_level": "Support Level", "price": "Price", "free": "Free", "basic": "Basic", "premium": "Premium", "enterprise": "Enterprise", "choose_plan": "Choose <PERSON>", "choose_plan_title": "Choose Your Plan", "choose_plan_subtitle": "Select the perfect plan for your streaming needs", "streaming_usage": "Streaming", "storage_usage": "Storage", "slots_used": "slots used", "used": "used", "expires": "Expires", "most_popular": "Most Popular", "unlimited": "Unlimited", "streaming_slot": "Streaming Slot", "storage": "Storage", "basic_features": "Basic Features", "get_started": "Get Started", "upgrade": "Upgrade", "subscribe": "Subscribe", "change_plan": "Change Plan", "upgrade_plan_btn": "Upgrade Plan", "downgrade_not_available": "Downgrade (Not Available)", "faq_title": "Frequently Asked Questions", "faq_change_plan_q": "Can I upgrade my plan anytime?", "faq_change_plan_a": "Yes, you can upgrade to a higher plan at any time. Changes take effect immediately after payment confirmation.", "faq_exceed_limits_q": "What happens if I exceed my limits?", "faq_exceed_limits_a": "If you reach your streaming or storage limits, you'll need to upgrade your plan or free up space to continue using the service.", "faq_subscription_management_q": "How do I manage my subscription?", "faq_subscription_management_a": "You can upgrade your plan anytime through the subscription page. For other subscription changes or support, please contact our customer service team.", "subscribe_success": "Successfully subscribed to plan!", "subscribe_error": "Error", "subscribe_failed": "Failed to subscribe. Please try again.", "trial_active": "Trial Active", "trial_access": "Trial Access", "trial_expires": "Trial expires", "current": "Current", "popular": "Popular", "best_value": "Best Value"}, "settings": {"title": "Account <PERSON><PERSON>", "profile": "Profile Settings", "account": "Account <PERSON><PERSON>", "security": "Security", "preferences": "Preferences", "notifications": "Notification Settings", "profile_picture": "Profile Picture", "change_password": "Change Password", "current_password": "Current Password", "new_password": "New Password", "confirm_new_password": "Confirm New Password", "update_profile": "Update Profile", "delete_account": "Delete Account", "language_preference": "Language Preference", "timezone": "Timezone", "email_notifications": "Email Notifications", "push_notifications": "Push Notifications", "stream_notifications": "Stream Notifications", "security_notifications": "Security Notifications"}, "admin": {"title": "Admin Dashboard", "subtitle": "System overview and management", "welcome": "Welcome, {{username}}", "users": "Users", "plans": "Plans", "performance": "Performance", "notifications": "Notifications", "load_balancer": "<PERSON><PERSON>r", "system_stats": "System Statistics", "user_management": "User Management", "plan_management": "Plan Management", "create_user": "Create User", "delete_user": "Delete User", "delete_users": "Delete Users", "activate_user": "Activate User", "deactivate_user": "Deactivate User", "delete_plan": "Delete Plan", "total_users": "Total Users", "active_users": "Active Users", "total_streams": "Total Streams", "live_streams": "Live Streams", "total_videos": "Total Videos", "active_subscriptions": "Active Subscriptions", "server_load": "Server Load", "memory_usage": "Memory Usage", "cpu_usage": "CPU Usage", "disk_usage": "Disk Usage", "network_usage": "Network Usage", "active_stat": "active", "total": "total", "used": "used", "cores": "cores", "recent_users": "Recent Users", "subscription_plans": "Subscription Plans", "view_all": "View All", "manage": "Manage", "no_email": "No email", "subscribers": "Subscribers", "slots": "slots", "unlimited": "Unlimited", "manage_users": "Manage Users", "manage_users_desc": "View and manage user accounts", "subscription_plans_desc": "Configure pricing and features", "analytics": "Analytics", "analytics_desc": "View detailed statistics", "load_balancer_desc": "Manage automatic quality adjustment", "performance_desc": "Monitor system performance and optimization", "stream_monitor": "Stream Monitor", "stream_monitor_desc": "Monitor and control FFmpeg processes", "user_management_title": "User Management", "user_management_subtitle": "Manage user accounts, roles, and permissions", "select_action": "Select Action", "activate_users": "Activate Users", "deactivate_users": "Deactivate Users", "change_plan": "Change Plan", "apply": "Apply", "clear": "Clear", "refresh": "Refresh", "admins": "Admins", "premium_users": "Premium Users", "all_users": "All Users", "search_users": "Search users...", "user": "User", "role": "Role", "plan": "Plan", "storage": "Storage", "status": "Status", "actions": "Actions", "user_active": "Active", "inactive": "Inactive", "edit_user": "Edit User", "username": "Username", "email": "Email", "max_streaming_slots": "Max Streaming Slots", "max_storage": "Max Storage", "use_unlimited": "Use -1 for unlimited", "choose_mb_smaller": "Choose MB for smaller storage limits (e.g., 500MB)", "account_active": "Account Active", "reset_password": "Reset Password", "leave_empty_password": "Leave empty to keep current password", "generate": "Generate", "update_user": "Update User", "mb_used": "MB used", "gb_used": "GB used", "subscription_plans_title": "Subscription Plans", "subscription_plans_subtitle": "Manage subscription plans and pricing", "activate_plans": "Activate Plans", "deactivate_plans": "Deactivate Plans", "delete_plans": "Delete Plans", "add_plan": "Add Plan", "most_popular": "Most Popular", "streaming_slot": "Streaming Slot", "streaming_slots": "Streaming Slots", "basic_support": "Basic Support", "active_subscribers": "Active Subscribers", "edit": "Edit", "plan_details": "Plan Details", "plan_name": "Plan Name", "price": "Price", "add_new_plan": "Add New Plan", "currency": "<PERSON><PERSON><PERSON><PERSON>", "billing_period": "Billing Period", "monthly": "Monthly", "yearly": "Yearly", "use_zero_free": "Use 0 for free plans", "use_unlimited_disable": "Use -1 for unlimited, 0 to disable streaming", "use_zero_disable_storage": "Use 0 to disable storage, choose MB for smaller limits (e.g., 500MB)", "features": "Features", "enter_feature": "Enter feature", "create_plan": "Create Plan", "edit_plan": "Edit Plan", "update_plan": "Update Plan", "plan_subscribers": "Plan Subscribers", "loading_subscribers": "Loading subscribers...", "close": "Close", "no_subscribers_found": "No subscribers found for this plan", "delete_plan_confirm": "Are you sure you want to delete this plan? This action cannot be undone.", "per": "per", "subscription_management": "Subscription Management", "subscription_management_desc": "Manage user subscriptions and billing", "subscriptions": "Subscriptions", "create_subscription": "Create Subscription", "extend_subscription": "Extend Subscription", "cancel_subscription": "Cancel Subscription", "subscription_history": "Subscription History", "start_date": "Start Date", "end_date": "End Date", "payment_method": "Payment Method", "subscription_status": "Status", "extension_days": "Extension Days", "custom_end_date": "Custom End Date", "use_prorated": "Use Prorated Calculation", "admin_manual": "Admin Manual", "subscription_created": "Subscription created successfully", "subscription_extended": "Subscription extended successfully", "subscription_cancelled": "Subscription cancelled successfully", "plan_updated": "Plan updated successfully", "view_history": "View History", "extend": "Extend", "cancel": "Cancel", "sub_active": "Active", "expired": "Expired", "cancelled": "Cancelled", "upgraded": "Upgraded", "performance_monitoring": "Performance Monitoring", "performance_monitoring_subtitle": "Monitor system performance, cache efficiency, and database optimization", "refresh_data": "Refresh Data", "clear_cache": "<PERSON>ache", "avg_response_time": "Avg Response Time", "milliseconds": "milliseconds", "cache_hit_rate": "<PERSON><PERSON> Hit Rate", "entries": "entries", "db_queries": "DB Queries", "slow_queries": "slow queries", "error_rate": "Error Rate", "requests": "requests", "performance_alerts": "Performance Alerts", "performance_recommendations": "Performance Recommendations", "suggestion": "Suggestion", "system_metrics": "System Metrics", "hours_uptime": "Hours Uptime", "active_streams": "Active Streams", "database_performance": "Database Performance", "avg_query_time": "Avg Query Time (ms)", "database_size": "Database Size (MB)", "tables_indexes": "Tables & Indexes", "indexes": "indexes", "cache_performance": "<PERSON><PERSON>", "cache_hits": "<PERSON><PERSON>", "cache_misses": "<PERSON><PERSON>", "cache_entries": "Cache Entries", "cache_usage": "<PERSON><PERSON>", "recent_performance_history": "Recent Performance History", "response_times_last_10": "Response Times (Last 10 requests)", "database_query_times_last_10": "Database Query Times (Last 10 queries)", "clear_cache_confirm": "Are you sure you want to clear all cache? This may temporarily slow down the application.", "cache_cleared_success": "<PERSON><PERSON> cleared successfully!", "failed_clear_cache": "Failed to clear cache", "admin_notifications": "Admin Notifications", "admin_notifications_subtitle": "Manage system notifications and alerts", "mark_all_read": "<PERSON>", "cleanup_old": "Cleanup Old", "test_notification": "Test Notification", "create_samples": "Create Samples", "total_notifications": "Total Notifications", "unread": "Unread", "critical": "Critical", "system_alerts": "System Alerts", "filters": "Filters", "type": "Type", "all_types": "All Types", "info": "Info", "warning": "Warning", "error": "Error", "success": "Success", "category": "Category", "all_categories": "All Categories", "system": "System", "streams": "Streams", "priority": "Priority", "all_priorities": "All Priorities", "low": "Low", "normal": "Normal", "high": "High", "all": "All", "read": "Read", "loading_notifications": "Loading notifications...", "no_notifications_found": "No notifications found", "create_test_notification": "Create Test Notification", "message": "Message", "test_notification_title": "Test Notification", "test_notification_message": "This is a test notification message.", "create": "Create", "mark_all_read_confirm": "Are you sure you want to mark all notifications as read?", "cleanup_old_confirm": "Are you sure you want to delete old notifications (older than 30 days)?", "create_samples_confirm": "This will create 5 sample notifications. Continue?", "delete_notification_confirm": "Are you sure you want to delete this notification?", "mark_as_read": "<PERSON> as read", "delete_notification": "Delete notification", "give_trial": "Give Trial", "remove_trial": "Remove Trial", "trial_duration": "Trial Duration", "trial_duration_days": "Duration (Days)", "trial_slots": "Trial Slots", "trial_storage": "Trial Storage (MB)", "give_trial_confirm": "Are you sure you want to give trial access to this user?", "remove_trial_confirm": "Are you sure you want to remove trial access from this user?", "trial_given_success": "Trial access given successfully", "trial_removed_success": "Trial access removed successfully", "user_has_active_trial": "User already has an active trial", "trial_expires_on": "Trial expires on", "active_trial": "Active Trial", "marked_notifications_as_read": "notifications marked as read", "failed_to_mark_as_read": "Failed to mark notifications as read", "error_marking_as_read": "Error marking notifications as read", "failed_to_cleanup": "Failed to cleanup notifications", "error_cleaning_up": "Error cleaning up notifications", "test_notification_created": "Test notification created", "failed_to_create_test": "Failed to create test notification", "error_creating_test": "Error creating test notification", "failed_to_create_samples": "Failed to create sample notifications", "error_creating_samples": "Error creating sample notifications", "notification_deleted": "Notification deleted", "failed_to_delete": "Failed to delete notification", "error_deleting": "Error deleting notification", "page": "Page", "failed_to_load": "Failed to load notifications", "error_loading": "Error loading notifications", "unable_to_connect": "Unable to connect to server. Please check if the server is running.", "stream_status_sync": "Stream Status Sync", "stream_status_sync_desc": "Monitor and synchronize stream status between database and active processes", "sync_now": "Sync Now", "database_live": "Database Live", "memory_active": "Memory Active", "inconsistencies": "Inconsistencies"}, "notifications": {"title": "Notifications", "mark_all_read": "Mark all as read", "view_all": "View all", "no_notifications": "No notifications", "stream_started": "Stream started", "stream_stopped": "Stream stopped", "stream_error": "Stream error", "upload_complete": "Upload complete", "upload_failed": "Upload failed", "quota_exceeded": "<PERSON><PERSON><PERSON> exceeded", "plan_upgraded": "Plan upgraded", "plan_downgraded": "Plan downgraded", "payment_success": "Payment successful", "payment_failed": "Payment failed", "new_notification_received": "New notification received"}, "errors": {"general_error": "Something went wrong", "network_error": "Network error occurred", "server_error": "Server error occurred", "not_found": "Page not found", "unauthorized": "Unauthorized access", "forbidden": "Access forbidden", "validation_error": "Validation error", "file_too_large": "File size too large", "invalid_file_type": "Invalid file type", "quota_exceeded": "Storage quota exceeded", "stream_limit_reached": "Stream limit reached", "invalid_stream_key": "Invalid stream key", "stream_not_found": "Stream not found", "video_not_found": "Video not found", "user_not_found": "User not found", "plan_not_found": "Plan not found"}, "success": {"stream_created": "Stream created successfully", "stream_updated": "Stream updated successfully", "stream_deleted": "Stream deleted successfully", "stream_started": "Stream started successfully", "stream_stopped": "Stream stopped successfully", "video_uploaded": "Video uploaded successfully", "video_deleted": "Video deleted successfully", "profile_updated": "Profile updated successfully", "password_changed": "Password changed successfully", "settings_saved": "Setting<PERSON> saved successfully", "plan_updated": "Plan updated successfully", "notification_sent": "Notification sent successfully"}, "referral": {"title": "Referral Program", "subtitle": "Invite friends and earn 5% commission from every plan purchase", "dashboard": "Referral Dashboard", "my_referrals": "My Referrals", "referral_link": "Referral Link", "referral_code": "Referral Code", "copy_link": "Copy Link", "copy_code": "Copy Code", "share_link": "Share this link with your friends to earn commission when they subscribe", "balance": "Referral Balance", "total_earnings": "Total Earnings", "pending_referrals": "Pending Referrals", "successful_referrals": "Successful Referrals", "commission_rate": "Commission Rate", "withdraw_balance": "Withdraw Balance", "minimum_withdrawal": "Minimum Withdrawal", "withdrawal_amount": "<PERSON><PERSON><PERSON> Amount", "bank_name": "Bank Name", "account_number": "Account Number", "account_name": "Account Holder Name", "submit_withdrawal": "Sub<PERSON>", "withdrawal_history": "Withdrawal History", "earnings_history": "Earnings History", "referral_stats": "Referral Statistics", "total_clicks": "Total Clicks", "total_signups": "Total Signups", "conversion_rate": "Conversion Rate", "commission_from": "Commission from", "no_earnings": "No earnings received yet", "share_referral_link": "Share your referral link to start earning commissions", "no_pending_referrals": "No pending referrals", "share_for_more_signups": "Share your referral link to get more signups", "no_successful_referrals": "No successful referrals yet", "wait_for_purchases": "Wait for your referrals to purchase a plan", "no_withdrawal_history": "No withdrawal history yet", "withdrawal_history_appear": "Your withdrawal history will appear here", "loading_data": "Loading data...", "refresh": "Refresh", "cancel": "Cancel", "processing": "Processing...", "success": "Success!", "referral_code_copied": "Referral code copied successfully!", "referral_link_copied": "Referral link copied successfully!", "withdrawal_submitted": "Withdrawal request submitted successfully!", "withdrawal_failed": "Failed to submit withdrawal", "withdrawal_error": "An error occurred while submitting withdrawal", "earnings_updated": "Earnings history updated successfully", "earnings_load_failed": "Failed to load earnings history", "referral_details_failed": "Failed to load referral details", "withdrawal_history_failed": "Failed to load withdrawal history", "try_again": "Try Again", "not_purchased_plan": "Haven't purchased plan", "status": {"pending": "Pending", "approved": "Approved", "rejected": "Rejected", "completed": "Completed"}, "placeholders": {"minimum_amount": "Minimum Rp 50,000", "bank_example": "Example: BCA Bank", "account_number_placeholder": "Bank account number", "account_name_placeholder": "Name as per bank account"}, "validation": {"amount_required": "Withdrawal amount is required", "bank_required": "Bank name is required", "account_number_required": "Account number is required", "account_name_required": "Account holder name is required"}, "how_it_works": {"title": "How Referral Program Works", "description": "By activating the referral program, you will get a unique code that can be shared with your friends. Earn <strong>5%</strong> commission from the <strong>first purchase</strong> of each friend who subscribes using your referral code.", "note": "Commission is only given for the first purchase of each referral, not for subsequent upgrades or renewals."}, "available_balance": "Available balance", "total_link_clicks": "Total link clicks", "processing_info": "Withdrawals will be processed manually by admin within 1-3 business days.", "loading_withdrawal_history": "Loading withdrawal history...", "your_referral_code": "Your Referral Code", "minimum_withdrawal_info": "Minimum withdrawal is Rp 50,000"}, "admin_referral": {"title": "Referral Settlements", "subtitle": "Manage referral balance withdrawal requests", "withdrawal_requests": "Withdrawal Requests", "total_referrals": "Total Referrals", "total_referrals_desc": "Total referrals created", "completed_referrals": "Completed", "completed_referrals_desc": "Successful referrals", "total_commissions": "Total Commissions", "total_commissions_desc": "Total commissions paid", "all_status": "All Status", "user": "User", "amount": "Amount", "bank": "Bank", "account": "Account", "status": "Status", "date": "Date", "actions": "Actions", "no_email": "No email", "approve": "Approve", "reject": "Reject", "processed": "Processed", "note": "Note", "no_withdrawal_requests": "No withdrawal requests yet", "withdrawal_requests_appear": "User withdrawal requests will appear here", "process_withdrawal": "Process Withdrawal", "approve_withdrawal": "Approve Withdrawal", "reject_withdrawal": "Reject <PERSON>", "admin_notes": "Admin Notes (Optional)", "admin_notes_placeholder": "Add notes for user...", "processing_request": "Processing...", "withdrawal_approved": "Withdrawal approved successfully", "withdrawal_rejected": "<PERSON><PERSON><PERSON> rejected successfully", "withdrawal_process_failed": "Failed to process withdrawal", "withdrawal_process_error": "An error occurred while processing withdrawal", "refresh": "Refresh"}, "landing": {"nav": {"how_it_works": "How It Works", "features": "Features", "video_tutorial": "Video Tutorial", "pricing": "Pricing", "faq": "FAQ", "support": "Support", "login": "<PERSON><PERSON>", "get_started": "Get Started"}, "hero": {"title": "Automated Live Streaming Platform", "subtitle": "Transform your content into continuous live streams across multiple platforms with our cloud-based streaming solution.", "cta_primary": "Start Broadcasting", "cta_secondary": "Explore Features", "streaming_to": "Broadcasting to:", "stats": {"uptime": "99.9%", "uptime_label": "Reliability", "streaming": "24<span class=\"slash-fix\"></span>7", "streaming_label": "Continuous Streaming", "platforms": "5+", "platforms_label": "Platforms"}}, "features": {"title": "Powerful Features", "subtitle": "Advanced tools designed for seamless content broadcasting", "feature_1": {"title": "Continuous Content Broadcasting", "desc": "Stream your videos continuously to multiple platforms using our cloud infrastructure, eliminating the need for dedicated hardware."}, "feature_2": {"title": "Multi-Platform Distribution", "desc": "Simultaneously broadcast to YouTube, Facebook, Twitch, TikTok, and Instagram from a single control panel."}, "feature_3": {"title": "Smart Scheduling System", "desc": "Program your content delivery with intelligent scheduling that automatically manages your streaming timeline."}, "feature_4": {"title": "Intuitive Control Panel", "desc": "Manage all your streaming activities through our streamlined dashboard designed for content creators of all skill levels."}, "feature_5": {"title": "Enterprise-Grade Infrastructure", "desc": "Benefit from our robust cloud servers that deliver consistent performance with industry-leading uptime guarantees."}, "feature_6": {"title": "Dedicated Customer Success", "desc": "Access our expert support team through multiple channels whenever you need assistance with your streaming setup."}}, "how_it_works": {"title": "Getting Started", "subtitle": "Launch your streaming channel in three simple steps", "step_1": {"title": "Setup & Content Upload", "desc": "Create your account and upload the video content you want to broadcast continuously"}, "step_2": {"title": "Platform Integration", "desc": "Configure your streaming destinations by adding platform credentials and stream keys"}, "step_3": {"title": "Launch Broadcasting", "desc": "Activate your streams and monitor your content distribution from our centralized dashboard"}}, "video_tutorial": {"title": "Watch How It Works", "subtitle": "See StreamOnPod in action with our comprehensive video tutorial", "video_title": "StreamOnPod Complete Tutorial", "description": "Learn how to use StreamOnPod from start to finish. This tutorial covers everything from account setup to advanced streaming configurations.", "loading": "Loading video...", "duration": "15 minutes", "views": "Tutorial Video", "language": "Indonesian"}, "pricing": {"title": "Flexible Plans", "subtitle": "Select the perfect streaming solution for your content strategy", "free_trial": "Start with our free tier", "per_month": "Monthly", "streaming_slots": "Broadcasting Channels", "storage": "Content Storage", "basic_support": "Standard Support", "choose_free": "Begin Free Trial", "choose_plan": "Select Plan"}, "faq": {"title": "Common Questions", "subtitle": "Everything you need to know about our streaming platform", "q1": {"question": "Is dedicated hardware required for continuous streaming?", "answer": "Not at all! Our cloud-based infrastructure handles all the heavy lifting, allowing you to broadcast continuously without any local hardware requirements."}, "q2": {"question": "Can I broadcast to multiple platforms at once?", "answer": "Absolutely! Our platform supports simultaneous broadcasting to YouTube, Facebook, Twitch, TikTok, and Instagram through a unified interface."}, "q3": {"question": "What level of technical expertise is needed?", "answer": "None whatsoever. Our platform is built with simplicity in mind, featuring an intuitive interface that anyone can master quickly."}, "q4": {"question": "Is automated content scheduling available?", "answer": "Yes! Our smart scheduling system allows you to plan your content calendar and automate your broadcasting timeline with precision."}, "q5": {"question": "How do you handle technical issues?", "answer": "Our dedicated support team provides comprehensive assistance through multiple channels, ensuring your streaming operations run smoothly at all times."}}, "testimonials": {"title": "Creator Success Stories", "subtitle": "Hear from content creators who've transformed their streaming workflow", "testimonial_1": {"name": "<PERSON>", "role": "Content Creator", "text": "StreamOnPod revolutionized my content strategy. The automated scheduling freed up hours of my time to focus on creating better content."}, "testimonial_2": {"name": "<PERSON>", "role": "Gaming Streamer", "text": "The multi-platform broadcasting feature is incredible. I can reach audiences on all major platforms simultaneously without any technical hassle."}, "testimonial_3": {"name": "<PERSON>", "role": "Educational Content Producer", "text": "The reliability is outstanding. My educational streams run continuously without interruption, providing consistent value to my audience."}}, "cta": {"title": "Ready to Transform Your Content Strategy?", "subtitle": "Join the growing community of creators who've streamlined their broadcasting with StreamOnPod's intelligent platform.", "start_streaming": "Launch Your Channel", "contact_support": "Get Expert Help"}, "footer": {"description": "Cloud-powered streaming platform that enables continuous content broadcasting across multiple platforms through intelligent automation and enterprise-grade infrastructure.", "quick_links": "Navigation", "support_title": "Help & Support", "help_center": "Support Center", "contact_us": "Reach Out", "copyright": "Crafted with ❤️ for the creator community.", "terms_of_service": "Terms of Service", "privacy_policy": "Privacy Policy"}}, "privacy": {"title": "Privacy Policy", "subtitle": "StreamOnPod Privacy Policy", "intro": "StreamOnPod (\"we\", \"us\", \"our\") is committed to protecting and respecting your privacy. This Privacy Policy explains how we collect, use, disclose, and protect your personal information when using our services.", "consent": "By accessing or using StreamOnPod services, you agree to the practices described in this Privacy Policy.", "section_1": {"title": "Information We Collect", "intro": "We may collect the following information:", "content": ["Personal Identity Information (e.g., full name, email address, phone number)", "Technical Information (e.g., IP address, device type, browser used, access time, pages viewed)", "Transaction Information (e.g., payment data, products or services you purchase)", "Communication Information (e.g., messages, questions, or feedback you send to us)", "Usage Data (e.g., how you use our services, activities in applications or websites)"], "note": "We only collect data that is relevant and necessary to provide the best service to you."}, "section_2": {"title": "How We Use Your Information", "intro": "The information collected may be used to:", "content": ["Provide, operate, and maintain our services.", "Process transactions and manage your account.", "Send important service-related information (such as changes, updates, or alerts).", "Provide customer support.", "Develop, improve, and expand our services.", "Send promotions, offers, or marketing information (only if you agree).", "Analyze user behavior for service improvement.", "Prevent illegal activities or violations of terms of use."]}, "section_3": {"title": "Use of Cookies and Tracking Technologies", "intro": "We use cookies and similar technologies to:", "purposes": ["Improve user experience.", "Analyze service usage.", "Provide more relevant content."], "internal_cookies": {"title": "Internal Cookies", "description": "Cookies are used to store user preferences and optimize the services provided."}, "third_party_cookies": {"title": "Third-Party Cookies", "intro": "We also use the following third-party services:", "google_analytics": {"title": "Google Analytics", "description": "To help us understand how visitors use our site. Information collected includes, but is not limited to: pages visited, visit duration, user location (based on IP), and type of device used. This data is used for internal analysis purposes and is not shared for other commercial use."}, "meta_pixel": {"title": "<PERSON><PERSON> (Facebook Pixel)", "description": "To measure the effectiveness of our ads on Meta platforms (Facebook, Instagram) and to target more relevant audiences. Data collected may include activity on our site such as page visits and content interactions."}, "note": "Note: You can set cookie preferences through your browser settings or through the advertising settings of each platform."}}, "section_4": {"title": "Disclosure of Information to Third Parties", "intro": "We do not sell, rent, or exchange your personal information to third parties. However, we may share your information with:", "content": ["Third-party service providers such as hosting, payment, analytics, or email marketing providers.", "Authorities, when required by law or court order."], "note": "All such third parties are bound to maintain the confidentiality and security of your information."}, "section_5": {"title": "Data Security", "intro": "We implement reasonable technical and organizational measures to:", "content": ["Protect personal data from unauthorized access, use, or disclosure.", "Prevent loss, destruction, or alteration of data."], "disclaimer": "However, no data transmission system over the internet is 100% secure. We are committed to doing our best to protect your data."}, "section_6": {"title": "Data Storage and Retention", "content": ["Your personal data is stored for as long as necessary to fulfill the purpose of collection or as required by applicable law.", "You may request deletion of your data at any time, except where there are legal obligations that require us to retain it."]}, "section_7": {"title": "Your Rights as a User", "intro": "You have the right to:", "content": ["Access your personal data.", "Correct inaccurate data.", "Request deletion of your personal data.", "Withdraw previous consent.", "Object to the use of data for certain purposes."], "note": "Please contact us to exercise these rights."}, "section_8": {"title": "Changes to Privacy Policy", "content": ["We may update this Privacy Policy from time to time.", "Changes will take effect immediately after being posted on our platform, with appropriate notice if required."]}, "section_9": {"title": "Contact Us", "intro": "If you have questions, requests, or complaints regarding this Privacy Policy, you can contact us at:", "email": "Email: <EMAIL>", "telegram": "Telegram: @streamonpod"}, "conclusion": "By using StreamOnPod services, you declare that you have read, understood, and agreed to this Privacy Policy."}, "tos": {"title": "Terms of Service", "subtitle": "StreamOnPod Service Terms and Conditions", "intro": "Thank you for using StreamOnPod services! By accessing and using our services, you agree to be bound by the following Terms and Conditions. Please read carefully.", "section_1": {"title": "Definitions", "content": {"streamonpod": "StreamOnPod: A service that provides tools, resources, and services to support content creator needs, including but not limited to 24/7 live streaming tools and video management.", "user": "User: Individual or entity that accesses and/or uses StreamOnPod services.", "service": "Service: All products, features, content, applications, or services provided by StreamOnPod."}}, "section_2": {"title": "General Terms", "content": ["Users must comply with all terms contained in this document.", "StreamOnPod reserves the right to change, add, or remove parts of these Terms & Conditions at any time without prior notice.", "By continuing to use the service after changes, you are deemed to accept those changes."]}, "section_3": {"title": "Service Scope", "content": ["StreamOnPod provides tools and resources to facilitate the process of creating, managing, and optimizing digital content.", "Products offered include but are not limited to:", "• 24/7 live streaming automation", "• Video file management and storage", "• Multi-platform streaming support", "• Stream scheduling and management tools"]}, "section_4": {"title": "Ownership and Copyright", "content": ["All content, materials, and tools provided by StreamOnPod are the property of StreamOnPod or related license holders.", "Users are not permitted to reproduce, sell, distribute, or exploit StreamOnPod materials without written permission.", "StreamOnPod respects the intellectual property rights of others. If you feel your rights have been violated, please contact us."]}, "section_5": {"title": "User Responsibilities", "content": ["Users are fully responsible for the use of services and materials downloaded or used from StreamOnPod.", "Users must use the service in accordance with applicable laws in the Republic of Indonesia.", "Users are prohibited from using the service for illegal purposes, violating the rights of others, or harming third parties."]}, "section_6": {"title": "Payment and Refund Policy", "content": ["All payments are made according to the prices listed at the time of transaction.", "There are no refunds except for errors from StreamOnPod or otherwise agreed upon.", "StreamOnPod reserves the right to refuse service to anyone on reasonable grounds."]}, "section_7": {"title": "Limitation of Liability", "content": ["StreamOnPod is not responsible for direct, indirect, incidental, special, or consequential damages resulting from the use or inability to use the service.", "StreamOnPod does not guarantee that the service will always be free from interruptions, errors, or secure from third-party attacks."]}, "section_8": {"title": "Privacy", "content": ["StreamOnPod respects user privacy. Personal data collected will be used only for service operational purposes.", "StreamOnPod will not sell or share user data to third parties without permission, except as required by law."]}, "section_9": {"title": "Force Majeure", "content": ["StreamOnPod is not responsible for failure or delay in providing services due to events beyond reasonable control such as natural disasters, war, network failures, or government actions."]}, "section_10": {"title": "Applicable Law", "content": ["These Terms and Conditions are governed and interpreted based on the laws of the Republic of Indonesia.", "Any disputes that arise will be resolved through consultation. If not reached, it will be resolved through applicable legal channels in Indonesia."]}, "section_11": {"title": "Conclusion", "content": ["By using StreamOnPod services, you declare that you have read, understood, and agreed to all the terms above.", "If you do not agree with these Terms and Conditions, please do not use our services."]}, "contact": {"title": "Contact Us", "description": "For questions or further clarification, please contact:", "email": "Email: <EMAIL>", "telegram": "Telegram: @streamonpod"}}}